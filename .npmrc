# ===========================================
# pnpm 配置文件
# ===========================================
# 解决 Sentry 10.0.0 + Turbopack + OpenTelemetry 依赖问题
# 参考：https://docs.sentry.io/platforms/javascript/guides/nextjs/troubleshooting/

# 特定包提升 - 解决 import-in-the-middle 和 require-in-the-middle 问题
# 这是官方推荐的解决方案，比 shamefully-hoist=true 更精确
public-hoist-pattern[]=*import-in-the-middle*
public-hoist-pattern[]=*require-in-the-middle*

# 如果上述方案不工作，可以尝试以下备选方案：
# shamefully-hoist=true
# 注意：shamefully-hoist=true 会提升所有依赖，不是理想的依赖管理方式
# 但有时对于某些期望 Node.js 模块解析行为的包是必要的
