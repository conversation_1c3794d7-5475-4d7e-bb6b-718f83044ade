{"id": "c8099e32-ab23-4020-825b-e92645a29e4f", "name": "主题系统和字体配置", "description": "配置next-themes主题切换系统（系统/明亮/暗黑三模式），集成Geist字体和中文字体回退策略，建立CSS变量主题系统。采用现代化B2B企业设计风格。", "qualityAssurance": {"automatedChecks": {"tools": ["pnpm type-check:strict", "pnpm lint:strict", "pnpm format:check", "pnpm build", "pnpm test", "pnpm arch:validate", "pnpm security:check", "pnpm duplication:check", "pnpm size:check"], "executionMode": "sequential", "failFast": true, "scope": ["主题切换功能验证", "字体系统验证", "CSS变量系统验证", "代码质量检查", "架构一致性验证"], "threshold": "100%通过率", "estimatedTime": "90-120秒"}, "aiTechnicalReview": {"scope": ["技术实现质量", "最佳实践遵循", "企业级标准"], "threshold": "≥90分", "evaluationMethod": "基于自动化检查结果进行技术分析", "scoringCriteria": {"技术实现质量": "30分 - 主题系统实现正确性、字体配置完整性", "最佳实践遵循": "30分 - Next.js主题最佳实践、字体优化策略", "企业级标准": "25分 - 用户体验标准、性能优化、设计一致性", "项目整体影响": "15分 - 对后续任务的影响、设计基础建立"}, "focusAreas": ["主题切换性能", "字体加载优化", "用户体验流畅度"]}, "humanConfirmation": {"timeLimit": "≤6分钟", "method": "完整主题和字体系统验证", "items": ["验证三种主题模式切换无闪烁", "测试中英文字体混排效果", "确认主题在不同设备正确显示", "验证CSS变量系统正常工作", "测试现代化B2B设计风格一致性"], "prerequisite": "自动化检查100%通过 + AI审查≥90分"}}}