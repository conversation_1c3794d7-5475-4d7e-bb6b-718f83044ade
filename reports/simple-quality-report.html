
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>质量报告 - tucsenberg-web-frontier</title>
    <style>
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; margin: 0; padding: 20px; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; border-radius: 8px 8px 0 0; }
        .score { font-size: 3em; font-weight: bold; margin: 10px 0; }
        .content { padding: 30px; }
        .section { margin-bottom: 30px; }
        .section h2 { color: #333; border-bottom: 2px solid #667eea; padding-bottom: 10px; }
        .stats { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin: 20px 0; }
        .stat-card { background: #f8f9fa; padding: 20px; border-radius: 6px; text-align: center; }
        .stat-value { font-size: 2em; font-weight: bold; color: #667eea; }
        .check-item { display: flex; align-items: center; padding: 10px; margin: 5px 0; border-radius: 4px; }
        .check-pass { background: #d4edda; color: #155724; }
        .check-fail { background: #f8d7da; color: #721c24; }
        .check-icon { margin-right: 10px; font-size: 1.2em; }
        .recommendation { background: #fff3cd; border-left: 4px solid #ffc107; padding: 15px; margin: 10px 0; }
        .priority-high { border-left-color: #dc3545; }
        .priority-medium { border-left-color: #ffc107; }
        .priority-low { border-left-color: #28a745; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>质量报告</h1>
            <div class="score">50/100</div>
            <p>生成时间: 8/2/2025, 3:19:02 PM</p>
        </div>

        <div class="content">
            <div class="section">
                <h2>📊 项目统计</h2>
                <div class="stats">
                    <div class="stat-card">
                        <div class="stat-value">71</div>
                        <div>源文件</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value">5</div>
                        <div>测试文件</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value">14444</div>
                        <div>代码行数</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value">85%</div>
                        <div>测试覆盖率</div>
                    </div>
                </div>
            </div>

            <div class="section">
                <h2>✅ 质量检查结果</h2>
                
                    <div class="check-item check-fail">
                        <span class="check-icon">❌</span>
                        <span>TypeScript类型检查</span>
                    </div>
                
                    <div class="check-item check-fail">
                        <span class="check-icon">❌</span>
                        <span>Prettier格式检查</span>
                    </div>
                
                    <div class="check-item check-pass">
                        <span class="check-icon">✅</span>
                        <span>单元测试</span>
                    </div>
                
            </div>

            <div class="section">
                <h2>💡 改进建议</h2>
                
                    <div class="recommendation priority-high">
                        <h4>修复失败的质量检查</h4>
                        <p>发现 2 个质量问题需要修复</p>
                        <strong>建议行动:</strong> 查看详细错误信息并逐一修复
                    </div>
                
            </div>
        </div>
    </div>
</body>
</html>