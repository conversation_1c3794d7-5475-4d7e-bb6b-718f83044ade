{"statistics": {"detectionDate": "2025-08-02T06:52:35.022Z", "formats": {"typescript": {"sources": {"/Users/<USER>/Warehouse/Focus/tucsenberg-web-frontier/src/app/api/test-content/route.ts": {"lines": 85, "tokens": 689, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "/Users/<USER>/Warehouse/Focus/tucsenberg-web-frontier/src/features/dashboard/index.ts": {"lines": 8, "tokens": 54, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "/Users/<USER>/Warehouse/Focus/tucsenberg-web-frontier/src/features/auth/index.ts": {"lines": 8, "tokens": 59, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "/Users/<USER>/Warehouse/Focus/tucsenberg-web-frontier/src/components/ui/sheet.tsx": {"lines": 159, "tokens": 977, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "/Users/<USER>/Warehouse/Focus/tucsenberg-web-frontier/src/components/ui/separator.tsx": {"lines": 25, "tokens": 186, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "/Users/<USER>/Warehouse/Focus/tucsenberg-web-frontier/src/components/ui/navigation-menu.tsx": {"lines": 168, "tokens": 948, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "/Users/<USER>/Warehouse/Focus/tucsenberg-web-frontier/src/components/ui/label.tsx": {"lines": 24, "tokens": 138, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "/Users/<USER>/Warehouse/Focus/tucsenberg-web-frontier/src/components/ui/input.tsx": {"lines": 20, "tokens": 130, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "/Users/<USER>/Warehouse/Focus/tucsenberg-web-frontier/src/components/ui/dropdown-menu.tsx": {"lines": 275, "tokens": 1562, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "/Users/<USER>/Warehouse/Focus/tucsenberg-web-frontier/src/components/ui/card.tsx": {"lines": 91, "tokens": 576, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "/Users/<USER>/Warehouse/Focus/tucsenberg-web-frontier/src/components/ui/button.tsx": {"lines": 59, "tokens": 366, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "/Users/<USER>/Warehouse/Focus/tucsenberg-web-frontier/src/components/ui/badge.tsx": {"lines": 39, "tokens": 247, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "/Users/<USER>/Warehouse/Focus/tucsenberg-web-frontier/src/components/theme/theme-toggle-button.tsx": {"lines": 60, "tokens": 377, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "/Users/<USER>/Warehouse/Focus/tucsenberg-web-frontier/src/components/theme/theme-performance-monitor.tsx": {"lines": 91, "tokens": 567, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "/Users/<USER>/Warehouse/Focus/tucsenberg-web-frontier/src/components/theme/theme-menu-item.tsx": {"lines": 64, "tokens": 405, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "/Users/<USER>/Warehouse/Focus/tucsenberg-web-frontier/src/components/i18n/translation-preloader.tsx": {"lines": 258, "tokens": 1646, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "/Users/<USER>/Warehouse/Focus/tucsenberg-web-frontier/src/components/i18n/translation-fallback.tsx": {"lines": 197, "tokens": 1384, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "/Users/<USER>/Warehouse/Focus/tucsenberg-web-frontier/src/components/i18n/performance-dashboard.tsx": {"lines": 220, "tokens": 1539, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "/Users/<USER>/Warehouse/Focus/tucsenberg-web-frontier/src/components/i18n/locale-detection-demo.tsx": {"lines": 236, "tokens": 1868, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "/Users/<USER>/Warehouse/Focus/tucsenberg-web-frontier/src/components/i18n/format-helpers.tsx": {"lines": 217, "tokens": 1886, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "/Users/<USER>/Warehouse/Focus/tucsenberg-web-frontier/src/components/i18n/enhanced-locale-switcher.tsx": {"lines": 337, "tokens": 2279, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "/Users/<USER>/Warehouse/Focus/tucsenberg-web-frontier/src/app/error-test/page.tsx": {"lines": 234, "tokens": 1636, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "/Users/<USER>/Warehouse/Focus/tucsenberg-web-frontier/src/app/[locale]/page.tsx": {"lines": 223, "tokens": 1434, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "/Users/<USER>/Warehouse/Focus/tucsenberg-web-frontier/src/app/[locale]/layout.tsx": {"lines": 143, "tokens": 887, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "/Users/<USER>/Warehouse/Focus/tucsenberg-web-frontier/src/types/translation-manager.ts": {"lines": 98, "tokens": 558, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "/Users/<USER>/Warehouse/Focus/tucsenberg-web-frontier/src/types/index.ts": {"lines": 12, "tokens": 23, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "/Users/<USER>/Warehouse/Focus/tucsenberg-web-frontier/src/types/i18n.ts": {"lines": 310, "tokens": 2052, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "/Users/<USER>/Warehouse/Focus/tucsenberg-web-frontier/src/types/i18n-enhanced.ts": {"lines": 295, "tokens": 1747, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "/Users/<USER>/Warehouse/Focus/tucsenberg-web-frontier/src/types/global.ts": {"lines": 154, "tokens": 717, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "/Users/<USER>/Warehouse/Focus/tucsenberg-web-frontier/src/types/content.ts": {"lines": 170, "tokens": 1085, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "/Users/<USER>/Warehouse/Focus/tucsenberg-web-frontier/src/shared/utils.ts": {"lines": 34, "tokens": 84, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "/Users/<USER>/Warehouse/Focus/tucsenberg-web-frontier/src/services/url-generator.ts": {"lines": 303, "tokens": 2021, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "/Users/<USER>/Warehouse/Focus/tucsenberg-web-frontier/src/lib/utils.ts": {"lines": 5, "tokens": 61, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "/Users/<USER>/Warehouse/Focus/tucsenberg-web-frontier/src/lib/translation-validators.ts": {"lines": 232, "tokens": 1723, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "/Users/<USER>/Warehouse/Focus/tucsenberg-web-frontier/src/lib/translation-quality.ts": {"lines": 247, "tokens": 1611, "sources": 1, "clones": 1, "duplicatedLines": 14, "duplicatedTokens": 94, "percentage": 5.67, "percentageTokens": 5.83, "newDuplicatedLines": 0, "newClones": 0}, "/Users/<USER>/Warehouse/Focus/tucsenberg-web-frontier/src/lib/translation-quality-types.ts": {"lines": 56, "tokens": 325, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "/Users/<USER>/Warehouse/Focus/tucsenberg-web-frontier/src/lib/translation-manager.ts": {"lines": 442, "tokens": 3240, "sources": 1, "clones": 1, "duplicatedLines": 14, "duplicatedTokens": 94, "percentage": 3.17, "percentageTokens": 2.9, "newDuplicatedLines": 0, "newClones": 0}, "/Users/<USER>/Warehouse/Focus/tucsenberg-web-frontier/src/lib/translation-benchmarks.ts": {"lines": 145, "tokens": 837, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "/Users/<USER>/Warehouse/Focus/tucsenberg-web-frontier/src/lib/theme-analytics.ts": {"lines": 413, "tokens": 2757, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "/Users/<USER>/Warehouse/Focus/tucsenberg-web-frontier/src/lib/structured-data.ts": {"lines": 331, "tokens": 2193, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "/Users/<USER>/Warehouse/Focus/tucsenberg-web-frontier/src/lib/seo-metadata.ts": {"lines": 160, "tokens": 1222, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "/Users/<USER>/Warehouse/Focus/tucsenberg-web-frontier/src/lib/locale-storage.ts": {"lines": 341, "tokens": 2316, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "/Users/<USER>/Warehouse/Focus/tucsenberg-web-frontier/src/lib/locale-detection.ts": {"lines": 385, "tokens": 2554, "sources": 1, "clones": 2, "duplicatedLines": 22, "duplicatedTokens": 188, "percentage": 5.71, "percentageTokens": 7.36, "newDuplicatedLines": 0, "newClones": 0}, "/Users/<USER>/Warehouse/Focus/tucsenberg-web-frontier/src/lib/index.ts": {"lines": 12, "tokens": 23, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "/Users/<USER>/Warehouse/Focus/tucsenberg-web-frontier/src/lib/i18n-validation.ts": {"lines": 346, "tokens": 2666, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "/Users/<USER>/Warehouse/Focus/tucsenberg-web-frontier/src/lib/i18n-performance.ts": {"lines": 246, "tokens": 1944, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "/Users/<USER>/Warehouse/Focus/tucsenberg-web-frontier/src/lib/i18n-monitoring.ts": {"lines": 451, "tokens": 3406, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "/Users/<USER>/Warehouse/Focus/tucsenberg-web-frontier/src/lib/i18n-cache.ts": {"lines": 365, "tokens": 2878, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "/Users/<USER>/Warehouse/Focus/tucsenberg-web-frontier/src/lib/content.ts": {"lines": 389, "tokens": 2983, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "/Users/<USER>/Warehouse/Focus/tucsenberg-web-frontier/src/lib/colors.ts": {"lines": 328, "tokens": 2947, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "/Users/<USER>/Warehouse/Focus/tucsenberg-web-frontier/src/lib/accessibility.ts": {"lines": 451, "tokens": 3130, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "/Users/<USER>/Warehouse/Focus/tucsenberg-web-frontier/src/i18n/routing.ts": {"lines": 35, "tokens": 226, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "/Users/<USER>/Warehouse/Focus/tucsenberg-web-frontier/src/i18n/request.ts": {"lines": 158, "tokens": 1179, "sources": 1, "clones": 3, "duplicatedLines": 65, "duplicatedTokens": 481, "percentage": 41.14, "percentageTokens": 40.8, "newDuplicatedLines": 0, "newClones": 0}, "/Users/<USER>/Warehouse/Focus/tucsenberg-web-frontier/src/i18n/enhanced-request.ts": {"lines": 116, "tokens": 891, "sources": 1, "clones": 3, "duplicatedLines": 65, "duplicatedTokens": 481, "percentage": 56.03, "percentageTokens": 53.98, "newDuplicatedLines": 0, "newClones": 0}, "/Users/<USER>/Warehouse/Focus/tucsenberg-web-frontier/src/hooks/use-theme-toggle.ts": {"lines": 124, "tokens": 723, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "/Users/<USER>/Warehouse/Focus/tucsenberg-web-frontier/src/hooks/use-enhanced-translations.ts": {"lines": 219, "tokens": 1521, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "/Users/<USER>/Warehouse/Focus/tucsenberg-web-frontier/src/hooks/use-enhanced-theme.ts": {"lines": 204, "tokens": 1536, "sources": 1, "clones": 4, "duplicatedLines": 62, "duplicatedTokens": 458, "percentage": 30.39, "percentageTokens": 29.82, "newDuplicatedLines": 0, "newClones": 0}, "/Users/<USER>/Warehouse/Focus/tucsenberg-web-frontier/src/constants/i18n-constants.ts": {"lines": 251, "tokens": 1261, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "/Users/<USER>/Warehouse/Focus/tucsenberg-web-frontier/src/config/paths.ts": {"lines": 292, "tokens": 1929, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "/Users/<USER>/Warehouse/Focus/tucsenberg-web-frontier/src/components/theme-toggle.tsx": {"lines": 86, "tokens": 648, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "/Users/<USER>/Warehouse/Focus/tucsenberg-web-frontier/src/components/theme-provider.tsx": {"lines": 33, "tokens": 207, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "/Users/<USER>/Warehouse/Focus/tucsenberg-web-frontier/src/components/loading-spinner.tsx": {"lines": 48, "tokens": 330, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "/Users/<USER>/Warehouse/Focus/tucsenberg-web-frontier/src/components/language-toggle.tsx": {"lines": 114, "tokens": 877, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "/Users/<USER>/Warehouse/Focus/tucsenberg-web-frontier/src/components/error-boundary.tsx": {"lines": 97, "tokens": 674, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "/Users/<USER>/Warehouse/Focus/tucsenberg-web-frontier/src/app/page.tsx": {"lines": 5, "tokens": 36, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}, "/Users/<USER>/Warehouse/Focus/tucsenberg-web-frontier/src/app/layout.tsx": {"lines": 10, "tokens": 62, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}}, "total": {"lines": 11754, "tokens": 81043, "sources": 66, "clones": 7, "duplicatedLines": 121, "duplicatedTokens": 898, "percentage": 1.03, "percentageTokens": 1.11, "newDuplicatedLines": 0, "newClones": 0}}, "javascript": {"sources": {"/Users/<USER>/Warehouse/Focus/tucsenberg-web-frontier/src/services/url-generator-cjs.js": {"lines": 139, "tokens": 971, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}}, "total": {"lines": 139, "tokens": 971, "sources": 1, "clones": 0, "duplicatedLines": 0, "duplicatedTokens": 0, "percentage": 0, "percentageTokens": 0, "newDuplicatedLines": 0, "newClones": 0}}}, "total": {"lines": 11893, "tokens": 82014, "sources": 67, "clones": 7, "duplicatedLines": 121, "duplicatedTokens": 898, "percentage": 1.02, "percentageTokens": 1.09, "newDuplicatedLines": 0, "newClones": 0}}, "duplicates": [{"format": "typescript", "lines": 15, "fragment": "(issues: QualityIssue[]): string[] {\n    const suggestions = new Set<string>();\n\n    issues.forEach((issue) => {\n      if (issue.suggestion) {\n        suggestions.add(issue.suggestion);\n      }\n    });\n\n    return Array.from(suggestions);\n  }\n\n  /**\n   * 生成推荐\n   */", "tokens": 0, "firstFile": {"name": "/Users/<USER>/Warehouse/Focus/tucsenberg-web-frontier/src/lib/translation-manager.ts", "start": 389, "end": 403, "startLoc": {"line": 389, "column": 20, "position": 2890}, "endLoc": {"line": 403, "column": 6, "position": 2984}}, "secondFile": {"name": "/Users/<USER>/Warehouse/Focus/tucsenberg-web-frontier/src/lib/translation-quality.ts", "start": 237, "end": 248, "startLoc": {"line": 237, "column": 31, "position": 1519}, "endLoc": {"line": 248, "column": 2, "position": 1611}}}, {"format": "typescript", "lines": 12, "fragment": ") {\n        const normalizedLang = lang.toLowerCase();\n        // 安全的对象属性访问，避免对象注入\n        const detectedLocale = Object.prototype.hasOwnProperty.call(\n          BROWSER_LOCALE_MAP,\n          normalizedLang,\n        )\n          ? (BROWSER_LOCALE_MAP as Record<string, Locale>)[normalizedLang]\n          : undefined;\n\n        if (detectedLocale && SUPPORTED_LOCALES.includes(detectedLocale)) {\n          return", "tokens": 0, "firstFile": {"name": "/Users/<USER>/Warehouse/Focus/tucsenberg-web-frontier/src/lib/locale-detection.ts", "start": 356, "end": 367, "startLoc": {"line": 356, "column": 10, "position": 2345}, "endLoc": {"line": 367, "column": 7, "position": 2439}}, "secondFile": {"name": "/Users/<USER>/Warehouse/Focus/tucsenberg-web-frontier/src/lib/locale-detection.ts", "start": 204, "end": 215, "startLoc": {"line": 204, "column": 17, "position": 1316}, "endLoc": {"line": 215, "column": 6, "position": 1410}}}, {"format": "typescript", "lines": 38, "fragment": "(locale: string) {\n  return {\n    dateTime: {\n      short: {\n        day: 'numeric' as const,\n        month: 'short' as const,\n        year: 'numeric' as const,\n      },\n      long: {\n        day: 'numeric' as const,\n        month: 'long' as const,\n        year: 'numeric' as const,\n        weekday: 'long' as const,\n      },\n    },\n    number: {\n      precise: {\n        maximumFractionDigits: 5,\n      },\n      currency: {\n        style: 'currency' as const,\n        currency: locale === 'zh' ? 'CNY' : 'USD',\n      },\n      percentage: {\n        style: 'percent' as const,\n        minimumFractionDigits: 1,\n      },\n    },\n    list: {\n      enumeration: {\n        style: 'long' as const,\n        type: 'conjunction' as const,\n      },\n    },\n  };\n}\n\nfunction", "tokens": 0, "firstFile": {"name": "/Users/<USER>/Warehouse/Focus/tucsenberg-web-frontier/src/i18n/enhanced-request.ts", "start": 12, "end": 49, "startLoc": {"line": 12, "column": 19, "position": 57}, "endLoc": {"line": 49, "column": 9, "position": 322}}, "secondFile": {"name": "/Users/<USER>/Warehouse/Focus/tucsenberg-web-frontier/src/i18n/request.ts", "start": 14, "end": 51, "startLoc": {"line": 14, "column": 11, "position": 71}, "endLoc": {"line": 51, "column": 17, "position": 336}}}, {"format": "typescript", "lines": 15, "fragment": "(locale: string, loadTime: number) {\n  I18nPerformanceMonitor.recordLoadTime(loadTime);\n\n  const cache = TranslationCache.getInstance();\n  const cached = cache.get(`messages-${locale}`);\n  if (cached) {\n    I18nPerformanceMonitor.recordCacheHit();\n  } else {\n    I18nPerformanceMonitor.recordCacheMiss();\n  }\n\n  return Boolean(cached);\n}\n\nfunction", "tokens": 0, "firstFile": {"name": "/Users/<USER>/Warehouse/Focus/tucsenberg-web-frontier/src/i18n/enhanced-request.ts", "start": 49, "end": 63, "startLoc": {"line": 49, "column": 27, "position": 325}, "endLoc": {"line": 63, "column": 9, "position": 431}}, "secondFile": {"name": "/Users/<USER>/Warehouse/Focus/tucsenberg-web-frontier/src/i18n/request.ts", "start": 52, "end": 66, "startLoc": {"line": 52, "column": 19, "position": 341}, "endLoc": {"line": 66, "column": 15, "position": 447}}}, {"format": "typescript", "lines": 15, "fragment": "(locale),\n    metadata: {\n      loadTime: performance.now() - startTime,\n      cacheUsed: false,\n      error: true,\n      timestamp: Date.now(),\n    },\n  };\n}\n\nexport default getRequestConfig(async ({ requestLocale }) => {\n  const startTime = performance.now();\n  let locale = await requestLocale;\n\n  if", "tokens": 0, "firstFile": {"name": "/Users/<USER>/Warehouse/Focus/tucsenberg-web-frontier/src/i18n/enhanced-request.ts", "start": 89, "end": 103, "startLoc": {"line": 89, "column": 19, "position": 633}, "endLoc": {"line": 103, "column": 3, "position": 743}}, "secondFile": {"name": "/Users/<USER>/Warehouse/Focus/tucsenberg-web-frontier/src/i18n/request.ts", "start": 94, "end": 108, "startLoc": {"line": 94, "column": 11, "position": 667}, "endLoc": {"line": 108, "column": 12, "position": 777}}}, {"format": "typescript", "lines": 14, "fragment": ",\n) {\n  const startTime = performance.now();\n  const fromTheme = currentTheme || 'unknown';\n\n  if (!supportsViewTransitions()) {\n    originalSetTheme(newTheme);\n    const endTime = performance.now();\n    recordThemeSwitch(fromTheme, newTheme, startTime, endTime, false);\n    recordThemePreference(newTheme);\n    return;\n  }\n\n  const {", "tokens": 0, "firstFile": {"name": "/Users/<USER>/Warehouse/Focus/tucsenberg-web-frontier/src/hooks/use-enhanced-theme.ts", "start": 95, "end": 108, "startLoc": {"line": 95, "column": 2, "position": 722}, "endLoc": {"line": 108, "column": 2, "position": 827}}, "secondFile": {"name": "/Users/<USER>/Warehouse/Focus/tucsenberg-web-frontier/src/hooks/use-enhanced-theme.ts", "start": 54, "end": 67, "startLoc": {"line": 54, "column": 7, "position": 368}, "endLoc": {"line": 67, "column": 23, "position": 473}}}, {"format": "typescript", "lines": 19, "fragment": "});\n\n  transition.finished\n    .then(() => {\n      const endTime = performance.now();\n      recordThemeSwitch(fromTheme, newTheme, startTime, endTime, true);\n      recordThemePreference(newTheme);\n    })\n    .catch(() => {\n      const endTime = performance.now();\n      recordThemeSwitch(fromTheme, newTheme, startTime, endTime, true);\n      recordThemePreference(newTheme);\n    });\n}\n\n/**\n * 增强的主题切换hook，集成View Transitions API\n * 提供丝滑的主题切换动画效果，同时保持向后兼容\n */", "tokens": 0, "firstFile": {"name": "/Users/<USER>/Warehouse/Focus/tucsenberg-web-frontier/src/hooks/use-enhanced-theme.ts", "start": 142, "end": 160, "startLoc": {"line": 142, "column": 5, "position": 1094}, "endLoc": {"line": 160, "column": 4, "position": 1218}}, "secondFile": {"name": "/Users/<USER>/Warehouse/Focus/tucsenberg-web-frontier/src/hooks/use-enhanced-theme.ts", "start": 73, "end": 90, "startLoc": {"line": 73, "column": 3, "position": 549}, "endLoc": {"line": 90, "column": 4, "position": 673}}}], "filename": "/Users/<USER>/Warehouse/Focus/tucsenberg-web-frontier/node_modules/.pnpm/@jscpd+html-reporter@4.0.1/node_modules/@jscpd/html-reporter/dist/templates/main.pug"}