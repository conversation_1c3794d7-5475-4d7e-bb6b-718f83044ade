<!DOCTYPE html><html lang="en"><head><meta charset="UTF-8"><meta name="viewport" content="width=device-width, initial-scale=1.0"><title>Copy/Paste Detector Report</title><link href="styles/tailwind.css" rel="stylesheet"><link href="styles/prism.css" rel="stylesheet"></head><body class="bg-gray-100"><header class="bg-white shadow py-4"><div class="container mx-auto px-4"><h1 class="text-3xl font-semibold text-gray-800">jscpd - copy/paste report</h1></div></header><main class="container mx-auto my-8 p-4 bg-white shadow rounded"><section class="mb-8" id="dashboard"><h2 class="text-2xl font-semibold text-gray-700 mb-4">Dashboard</h2><div class="grid grid-cols-4 gap-4"><div class="bg-blue-200 p-4 rounded text-center"><h3 class="text-lg font-semibold text-blue-800 mb-2">Total Files</h3><span class="text-4xl font-bold text-blue-800">67</span></div><div class="bg-green-200 p-4 rounded text-center"><h3 class="text-lg font-semibold text-green-800 mb-2">Total Lines of Code</h3><span class="text-4xl font-bold text-green-800">11893</span></div><div class="bg-yellow-200 p-4 rounded text-center"><h3 class="text-lg font-semibold text-yellow-800 mb-2">Number of Clones</h3><span class="text-4xl font-bold text-yellow-800">7</span></div><div class="bg-red-200 p-4 rounded text-center"><h3 class="text-lg font-semibold text-red-800 mb-2">Duplicated Lines</h3><span class="text-4xl font-bold text-red-800">121 (1.02%)</span></div></div></section><section class="mb-8" id="formats"><h2 class="text-2xl font-semibold text-gray-700 mb-4">Formats with Duplications</h2><table class="w-full table-auto"><thead><tr class="bg-gray-200 text-gray-600 uppercase text-sm leading-normal"><th class="py-3 px-6 text-left">Format</th><th class="py-3 px-6 text-left">Files</th><th class="py-3 px-6 text-left">Lines</th><th class="py-3 px-6 text-left">Clones</th><th class="py-3 px-6 text-left">Duplicated Lines</th><th class="py-3 px-6 text-left">Duplicated Tokens</th></tr></thead><tbody><tr class="bg-white border-b border-gray-200 text-gray-800 text-sm"><td class="py-3 px-6"><a class="text-blue-600 hover:underline" href="#typescript-clones">typescript</a></td><td class="py-3 px-6">66</td><td class="py-3 px-6">11754</td><td class="py-3 px-6">7</td><td class="py-3 px-6">121</td><td class="py-3 px-6">898</td></tr><tr class="bg-white border-b border-gray-200 text-gray-800 text-sm"><td class="py-3 px-6"><a class="text-blue-600 hover:underline" href="#javascript-clones">javascript</a></td><td class="py-3 px-6">1</td><td class="py-3 px-6">139</td><td class="py-3 px-6">0</td><td class="py-3 px-6">0</td><td class="py-3 px-6">0</td></tr></tbody></table></section><section class="mb-8" id="txt-clones"><a name="typescript-clones"></a><h2 class="text-2xl font-semibold text-gray-700 mb-4">typescript</h2><div class="divide-y divide-gray-200 border-b-2"><div class="py-4"><p class="text-gray-600">/Users/<USER>/Warehouse/Focus/tucsenberg-web-frontier/src/lib/translation-manager.ts (Line 389:20 - Line 403:6), /Users/<USER>/Warehouse/Focus/tucsenberg-web-frontier/src/lib/translation-quality.ts (Line 237:31 - Line 248:2)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn0" onclick="toggleCodeBlock('cloneGroup0', 'expandBtn0', 'collapseBtn0')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn0" onclick="toggleCodeBlock('cloneGroup0', 'expandBtn0', 'collapseBtn0')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup0"><code class="language-typescript text-sm text-gray-800">(issues: QualityIssue[]): string[] {
    const suggestions = new Set&lt;string&gt;();

    issues.forEach((issue) =&gt; {
      if (issue.suggestion) {
        suggestions.add(issue.suggestion);
      }
    });

    return Array.from(suggestions);
  }

  /**
   * 生成推荐
   */</code></pre></div><div class="py-4"><p class="text-gray-600">/Users/<USER>/Warehouse/Focus/tucsenberg-web-frontier/src/lib/locale-detection.ts (Line 356:10 - Line 367:7), /Users/<USER>/Warehouse/Focus/tucsenberg-web-frontier/src/lib/locale-detection.ts (Line 204:17 - Line 215:6)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn1" onclick="toggleCodeBlock('cloneGroup1', 'expandBtn1', 'collapseBtn1')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn1" onclick="toggleCodeBlock('cloneGroup1', 'expandBtn1', 'collapseBtn1')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup1"><code class="language-typescript text-sm text-gray-800">) {
        const normalizedLang = lang.toLowerCase();
        // 安全的对象属性访问，避免对象注入
        const detectedLocale = Object.prototype.hasOwnProperty.call(
          BROWSER_LOCALE_MAP,
          normalizedLang,
        )
          ? (BROWSER_LOCALE_MAP as Record&lt;string, Locale&gt;)[normalizedLang]
          : undefined;

        if (detectedLocale &amp;&amp; SUPPORTED_LOCALES.includes(detectedLocale)) {
          return</code></pre></div><div class="py-4"><p class="text-gray-600">/Users/<USER>/Warehouse/Focus/tucsenberg-web-frontier/src/i18n/enhanced-request.ts (Line 12:19 - Line 49:9), /Users/<USER>/Warehouse/Focus/tucsenberg-web-frontier/src/i18n/request.ts (Line 14:11 - Line 51:17)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn2" onclick="toggleCodeBlock('cloneGroup2', 'expandBtn2', 'collapseBtn2')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn2" onclick="toggleCodeBlock('cloneGroup2', 'expandBtn2', 'collapseBtn2')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup2"><code class="language-typescript text-sm text-gray-800">(locale: string) {
  return {
    dateTime: {
      short: {
        day: 'numeric' as const,
        month: 'short' as const,
        year: 'numeric' as const,
      },
      long: {
        day: 'numeric' as const,
        month: 'long' as const,
        year: 'numeric' as const,
        weekday: 'long' as const,
      },
    },
    number: {
      precise: {
        maximumFractionDigits: 5,
      },
      currency: {
        style: 'currency' as const,
        currency: locale === 'zh' ? 'CNY' : 'USD',
      },
      percentage: {
        style: 'percent' as const,
        minimumFractionDigits: 1,
      },
    },
    list: {
      enumeration: {
        style: 'long' as const,
        type: 'conjunction' as const,
      },
    },
  };
}

function</code></pre></div><div class="py-4"><p class="text-gray-600">/Users/<USER>/Warehouse/Focus/tucsenberg-web-frontier/src/i18n/enhanced-request.ts (Line 49:27 - Line 63:9), /Users/<USER>/Warehouse/Focus/tucsenberg-web-frontier/src/i18n/request.ts (Line 52:19 - Line 66:15)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn3" onclick="toggleCodeBlock('cloneGroup3', 'expandBtn3', 'collapseBtn3')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn3" onclick="toggleCodeBlock('cloneGroup3', 'expandBtn3', 'collapseBtn3')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup3"><code class="language-typescript text-sm text-gray-800">(locale: string, loadTime: number) {
  I18nPerformanceMonitor.recordLoadTime(loadTime);

  const cache = TranslationCache.getInstance();
  const cached = cache.get(`messages-${locale}`);
  if (cached) {
    I18nPerformanceMonitor.recordCacheHit();
  } else {
    I18nPerformanceMonitor.recordCacheMiss();
  }

  return Boolean(cached);
}

function</code></pre></div><div class="py-4"><p class="text-gray-600">/Users/<USER>/Warehouse/Focus/tucsenberg-web-frontier/src/i18n/enhanced-request.ts (Line 89:19 - Line 103:3), /Users/<USER>/Warehouse/Focus/tucsenberg-web-frontier/src/i18n/request.ts (Line 94:11 - Line 108:12)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn4" onclick="toggleCodeBlock('cloneGroup4', 'expandBtn4', 'collapseBtn4')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn4" onclick="toggleCodeBlock('cloneGroup4', 'expandBtn4', 'collapseBtn4')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup4"><code class="language-typescript text-sm text-gray-800">(locale),
    metadata: {
      loadTime: performance.now() - startTime,
      cacheUsed: false,
      error: true,
      timestamp: Date.now(),
    },
  };
}

export default getRequestConfig(async ({ requestLocale }) =&gt; {
  const startTime = performance.now();
  let locale = await requestLocale;

  if</code></pre></div><div class="py-4"><p class="text-gray-600">/Users/<USER>/Warehouse/Focus/tucsenberg-web-frontier/src/hooks/use-enhanced-theme.ts (Line 95:2 - Line 108:2), /Users/<USER>/Warehouse/Focus/tucsenberg-web-frontier/src/hooks/use-enhanced-theme.ts (Line 54:7 - Line 67:23)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn5" onclick="toggleCodeBlock('cloneGroup5', 'expandBtn5', 'collapseBtn5')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn5" onclick="toggleCodeBlock('cloneGroup5', 'expandBtn5', 'collapseBtn5')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup5"><code class="language-typescript text-sm text-gray-800">,
) {
  const startTime = performance.now();
  const fromTheme = currentTheme || 'unknown';

  if (!supportsViewTransitions()) {
    originalSetTheme(newTheme);
    const endTime = performance.now();
    recordThemeSwitch(fromTheme, newTheme, startTime, endTime, false);
    recordThemePreference(newTheme);
    return;
  }

  const {</code></pre></div><div class="py-4"><p class="text-gray-600">/Users/<USER>/Warehouse/Focus/tucsenberg-web-frontier/src/hooks/use-enhanced-theme.ts (Line 142:5 - Line 160:4), /Users/<USER>/Warehouse/Focus/tucsenberg-web-frontier/src/hooks/use-enhanced-theme.ts (Line 73:3 - Line 90:4)</p><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2" id="expandBtn6" onclick="toggleCodeBlock('cloneGroup6', 'expandBtn6', 'collapseBtn6')">Show code</button><button class="bg-gray-500 text-white px-1 py-0.5 text-xs rounded focus:outline-none ml-2 hidden" id="collapseBtn6" onclick="toggleCodeBlock('cloneGroup6', 'expandBtn6', 'collapseBtn6')">Hide code</button><pre class="bg-gray-100 border border-gray-200 p-4 rounded mt-2 hidden" id="cloneGroup6"><code class="language-typescript text-sm text-gray-800">});

  transition.finished
    .then(() =&gt; {
      const endTime = performance.now();
      recordThemeSwitch(fromTheme, newTheme, startTime, endTime, true);
      recordThemePreference(newTheme);
    })
    .catch(() =&gt; {
      const endTime = performance.now();
      recordThemeSwitch(fromTheme, newTheme, startTime, endTime, true);
      recordThemePreference(newTheme);
    });
}

/**
 * 增强的主题切换hook，集成View Transitions API
 * 提供丝滑的主题切换动画效果，同时保持向后兼容
 */</code></pre></div><!-- Add more clone groups for .txt format as needed         // Add more clone groups for .txt format as needed--></div><a name="javascript-clones"></a><h2 class="text-2xl font-semibold text-gray-700 mb-4">javascript</h2><div class="divide-y divide-gray-200 border-b-2"><!-- Add more clone groups for .txt format as needed         // Add more clone groups for .txt format as needed--></div></section><!-- Add more sections for other formats and clone groups as needed--></main><footer class="bg-white shadow mt-8 py-4"><div class="container mx-auto px-4 text-center"><p class="text-sm text-gray-600">This report is generated by jscpd, an open-source copy/paste detector.</p><p class="text-sm text-gray-600">jscpd is licensed under the MIT License.</p><a class="text-blue-500 text-sm" href="https://github.com/kucherenko/jscpd" target="_blank" rel="noopener noreferrer">View jscpd on GitHub</a></div></footer><script src="js/prism.js"></script><script>function toggleCodeBlock(codeBlockId, expandBtnId, collapseBtnId) {
  const codeBlock = document.getElementById(codeBlockId);
  const expandBtn = document.getElementById(expandBtnId);
  const collapseBtn = document.getElementById(collapseBtnId);

  codeBlock.classList.toggle('hidden');
  expandBtn.classList.toggle('hidden');
  collapseBtn.classList.toggle('hidden');
}</script></body></html>