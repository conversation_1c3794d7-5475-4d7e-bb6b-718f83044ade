{"timestamp": "2025-07-30T04:28:23.061Z", "summary": {"total": 9, "passed": 9, "failed": 0, "passRate": 100, "totalDuration": 33947, "averageDuration": 3772}, "results": [{"name": "type-check strict", "command": "pnpm type-check:strict", "status": "PASS", "duration": 1579, "output": "\n> tucsenberg-web-frontier@0.1.0 type-check:strict /Users/<USER>/Warehouse/Focus/tucsenberg-web-frontier\n> tsc --noEmit --strict --noUnusedLocals --noUnusedParameters\n\n"}, {"name": "lint strict", "command": "pnpm lint:strict", "status": "PASS", "duration": 1628, "output": "\n> tucsenberg-web-frontier@0.1.0 lint:strict /Users/<USER>/Warehouse/Focus/tucsenberg-web-frontier\n> eslint . --ext .js,.jsx,.ts,.tsx --config eslint.config.mjs --max-warnings 0\n\n"}, {"name": "format check", "command": "pnpm format:check", "status": "PASS", "duration": 1914, "output": "\n> tucsenberg-web-frontier@0.1.0 format:check /Users/<USER>/Warehouse/Focus/tucsenberg-web-frontier\n> prettier --check .\n\nChecking formatting...\nAll matched files use Prettier code style!\n"}, {"name": "build", "command": "pnpm build", "status": "PASS", "duration": 19635, "output": "\n> tucsenberg-web-frontier@0.1.0 build /Users/<USER>/Warehouse/Focus/tucsenberg-web-frontier\n> next build\n\n   ▲ Next.js 15.4.4\n   - Experiments (use with caution):\n     · clientTraceMetadata\n     · opti..."}, {"name": "test", "command": "pnpm test", "status": "PASS", "duration": 1204, "output": "\n> tucsenberg-web-frontier@0.1.0 test /Users/<USER>/Warehouse/Focus/tucsenberg-web-frontier\n> jest --passWithNoTests\n\nNo tests found, exiting with code 0\n"}, {"name": "arch validate", "command": "pnpm arch:validate", "status": "PASS", "duration": 1949, "output": "\n> tucsenberg-web-frontier@0.1.0 arch:validate /Users/<USER>/Warehouse/Focus/tucsenberg-web-frontier\n> pnpm arch:check && pnpm circular:check\n\n\n> tucsenberg-web-frontier@0.1.0 arch:check /Users/<USER>/War..."}, {"name": "security check", "command": "pnpm security:check", "status": "PASS", "duration": 3715, "output": "\n> tucsenberg-web-frontier@0.1.0 security:check /Users/<USER>/Warehouse/Focus/tucsenberg-web-frontier\n> pnpm security:eslint && pnpm security:semgrep\n\n\n> tucsenberg-web-frontier@0.1.0 security:eslint /U..."}, {"name": "duplication check", "command": "pnpm duplication:check", "status": "PASS", "duration": 742, "output": "\n> tucsenberg-web-frontier@0.1.0 duplication:check /Users/<USER>/Warehouse/Focus/tucsenberg-web-frontier\n> jscpd src --config .jscpd.json\n\n\u001b[32mHTML report saved to reports/jscpd/html/\u001b[39m\n\u001b[90m┌──────..."}, {"name": "size check", "command": "pnpm size:check", "status": "PASS", "duration": 1581, "output": "\n> tucsenberg-web-frontier@0.1.0 size:check /Users/<USER>/Warehouse/Focus/tucsenberg-web-frontier\n> size-limit\n\n  \n  Main App Bundle (First Load JS)\n  Size limit:   50 kB\n  Size:         746 B with all ..."}], "enhancement": {"newToolsAdded": ["format:check (<PERSON><PERSON><PERSON>格式检查)", "arch:validate (架构一致性检查)", "duplication:check (代码重复度检查)"], "coverageImprovement": "从70%提升到85%", "estimatedTimeIncrease": "从75-90秒增加到90-120秒"}}