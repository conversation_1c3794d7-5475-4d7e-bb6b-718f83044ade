{"timestamp": "2025-07-30T14:49:09.013Z", "summary": {"totalFiles": 51, "scannedFiles": 51, "errorCount": 0, "warningCount": 0, "totalKeys": 124, "uniqueKeys": 34, "missingKeys": ["organization.name", "organization.description", "organization.phone", "organization.social.twitter", "organization.social.linkedin", "organization.social.github", "website.name", "website.description", "article.defaultAuthor", "title", "description", "siteName", "loading", "toggle", "english", "chinese", "error", "fallback<PERSON><PERSON>ning", "today", "yesterday", "tomorrow", "selectLanguage", "chineseTest", "chineseDescription", "chineseInput", "chinesePlaceholder", "englishTest", "englishDescription", "englishInput", "englishPlaceholder", "primaryButton", "secondaryButton", "outlineButton", "subtitle"], "unusedKeys": ["common.loading", "common.error", "common.success", "common.cancel", "common.confirm", "common.save", "common.edit", "common.delete", "common.search", "common.filter", "common.sort", "common.next", "common.previous", "common.close", "common.open", "navigation.home", "navigation.about", "navigation.contact", "navigation.services", "navigation.products", "navigation.blog", "navigation.login", "navigation.logout", "navigation.profile", "navigation.settings", "theme.toggle", "theme.light", "theme.dark", "theme.system", "language.toggle", "language.selectLanguage", "language.english", "language.chinese", "language.switching", "language.switchSuccess", "language.switchError", "language.fallback<PERSON><PERSON>ning", "language.detectionInfo", "language.source", "language.confidence", "language.userPreference", "home.title", "home.subtitle", "home.description", "home.getStarted", "home.learnMore", "home.features.title", "home.features.performance.title", "home.features.performance.description", "home.features.scalable.title", "home.features.scalable.description", "home.features.secure.title", "home.features.secure.description", "themeDemo.title", "themeDemo.description", "themeDemo.chineseTest", "themeDemo.englishTest", "themeDemo.chineseDescription", "themeDemo.englishDescription", "themeDemo.chineseInput", "themeDemo.englishInput", "themeDemo.chinesePlaceholder", "themeDemo.englishPlaceholder", "themeDemo.primaryButton", "themeDemo.secondaryButton", "themeDemo.outlineButton", "instructions.getStarted", "instructions.saveChanges", "actions.deployNow", "actions.readDocs", "footer.learn", "footer.examples", "footer.goToNextjs", "formatting.date.today", "formatting.date.yesterday", "formatting.date.tomorrow", "formatting.date.lastUpdated", "formatting.date.publishedOn", "formatting.number.currency", "formatting.number.percentage", "formatting.number.fileSize", "formatting.plurals.items.zero", "formatting.plurals.items.one", "formatting.plurals.items.other", "formatting.plurals.users.zero", "formatting.plurals.users.one", "formatting.plurals.users.other", "formatting.plurals.notifications.zero", "formatting.plurals.notifications.one", "formatting.plurals.notifications.other", "errors.notFound", "errors.serverError", "errors.networkError", "errors.validationError", "errors.unauthorized", "errors.forbidden", "errors.timeout", "errors.generic", "errors.translationMissing", "errors.loadingFailed", "accessibility.skipToContent", "accessibility.openMenu", "accessibility.closeMenu", "accessibility.loading", "accessibility.error", "accessibility.languageSelector", "accessibility.themeSelector", "seo.title", "seo.description", "seo.siteName", "seo.keywords", "structured-data.organization.name", "structured-data.organization.description", "structured-data.organization.phone", "structured-data.organization.social.twitter", "structured-data.organization.social.linkedin", "structured-data.organization.social.github", "structured-data.website.name", "structured-data.website.description", "structured-data.article.defaultTitle", "structured-data.article.defaultDescription", "structured-data.article.defaultAuthor", "structured-data.product.defaultName", "structured-data.product.defaultDescription"]}, "translationKeys": ["article.defaultAuthor", "chinese", "chineseDescription", "chineseInput", "chinesePlaceholder", "chineseTest", "description", "english", "englishDescription", "englishInput", "englishPlaceholder", "englishTest", "error", "fallback<PERSON><PERSON>ning", "loading", "organization.description", "organization.name", "organization.phone", "organization.social.github", "organization.social.linkedin", "organization.social.twitter", "outlineButton", "primaryButton", "secondaryButton", "selectLanguage", "siteName", "subtitle", "title", "today", "toggle", "tomorrow", "website.description", "website.name", "yesterday"], "keyUsages": {"organization.name": [{"file": "src/lib/structured-data.ts", "line": 68, "column": 25}, {"file": "src/lib/structured-data.ts", "line": 141, "column": 14}, {"file": "src/lib/structured-data.ts", "line": 181, "column": 28}, {"file": "src/lib/structured-data.ts", "line": 187, "column": 35}], "organization.description": [{"file": "src/lib/structured-data.ts", "line": 69, "column": 39}], "organization.phone": [{"file": "src/lib/structured-data.ts", "line": 76, "column": 33}], "organization.social.twitter": [{"file": "src/lib/structured-data.ts", "line": 81, "column": 6}], "organization.social.linkedin": [{"file": "src/lib/structured-data.ts", "line": 84, "column": 6}], "organization.social.github": [{"file": "src/lib/structured-data.ts", "line": 87, "column": 6}], "website.name": [{"file": "src/lib/structured-data.ts", "line": 105, "column": 25}], "website.description": [{"file": "src/lib/structured-data.ts", "line": 106, "column": 39}], "article.defaultAuthor": [{"file": "src/lib/structured-data.ts", "line": 137, "column": 8}], "title": [{"file": "src/lib/seo-metadata.ts", "line": 42, "column": 20}, {"file": "src/app/[locale]/page.tsx", "line": 97, "column": 52}, {"file": "src/app/[locale]/page.tsx", "line": 204, "column": 63}], "description": [{"file": "src/lib/seo-metadata.ts", "line": 45, "column": 4}, {"file": "src/app/[locale]/page.tsx", "line": 99, "column": 13}], "siteName": [{"file": "src/lib/seo-metadata.ts", "line": 48, "column": 19}], "loading": [{"file": "src/components/loading-spinner.tsx", "line": 45, "column": 14}], "toggle": [{"file": "src/components/language-toggle.tsx", "line": 77, "column": 37}, {"file": "src/components/i18n/enhanced-locale-switcher.tsx", "line": 232, "column": 37}], "english": [{"file": "src/components/language-toggle.tsx", "line": 91, "column": 19}], "chinese": [{"file": "src/components/language-toggle.tsx", "line": 110, "column": 19}], "error": [{"file": "src/components/error-boundary.tsx", "line": 80, "column": 51}], "fallbackWarning": [{"file": "src/components/i18n/translation-fallback.tsx", "line": 156, "column": 51}], "today": [{"file": "src/components/i18n/format-helpers.tsx", "line": 34, "column": 22}], "yesterday": [{"file": "src/components/i18n/format-helpers.tsx", "line": 36, "column": 22}], "tomorrow": [{"file": "src/components/i18n/format-helpers.tsx", "line": 38, "column": 22}], "selectLanguage": [{"file": "src/components/i18n/enhanced-locale-switcher.tsx", "line": 239, "column": 17}], "chineseTest": [{"file": "src/app/[locale]/page.tsx", "line": 105, "column": 53}], "chineseDescription": [{"file": "src/app/[locale]/page.tsx", "line": 107, "column": 17}], "chineseInput": [{"file": "src/app/[locale]/page.tsx", "line": 110, "column": 48}], "chinesePlaceholder": [{"file": "src/app/[locale]/page.tsx", "line": 113, "column": 31}], "englishTest": [{"file": "src/app/[locale]/page.tsx", "line": 119, "column": 53}], "englishDescription": [{"file": "src/app/[locale]/page.tsx", "line": 120, "column": 46}], "englishInput": [{"file": "src/app/[locale]/page.tsx", "line": 122, "column": 48}], "englishPlaceholder": [{"file": "src/app/[locale]/page.tsx", "line": 125, "column": 31}], "primaryButton": [{"file": "src/app/[locale]/page.tsx", "line": 132, "column": 21}], "secondaryButton": [{"file": "src/app/[locale]/page.tsx", "line": 133, "column": 41}], "outlineButton": [{"file": "src/app/[locale]/page.tsx", "line": 134, "column": 39}], "subtitle": [{"file": "src/app/[locale]/page.tsx", "line": 206, "column": 15}]}, "analysis": {"missingKeys": ["organization.name", "organization.description", "organization.phone", "organization.social.twitter", "organization.social.linkedin", "organization.social.github", "website.name", "website.description", "article.defaultAuthor", "title", "description", "siteName", "loading", "toggle", "english", "chinese", "error", "fallback<PERSON><PERSON>ning", "today", "yesterday", "tomorrow", "selectLanguage", "chineseTest", "chineseDescription", "chineseInput", "chinesePlaceholder", "englishTest", "englishDescription", "englishInput", "englishPlaceholder", "primaryButton", "secondaryButton", "outlineButton", "subtitle"], "unusedKeys": ["common.loading", "common.error", "common.success", "common.cancel", "common.confirm", "common.save", "common.edit", "common.delete", "common.search", "common.filter", "common.sort", "common.next", "common.previous", "common.close", "common.open", "navigation.home", "navigation.about", "navigation.contact", "navigation.services", "navigation.products", "navigation.blog", "navigation.login", "navigation.logout", "navigation.profile", "navigation.settings", "theme.toggle", "theme.light", "theme.dark", "theme.system", "language.toggle", "language.selectLanguage", "language.english", "language.chinese", "language.switching", "language.switchSuccess", "language.switchError", "language.fallback<PERSON><PERSON>ning", "language.detectionInfo", "language.source", "language.confidence", "language.userPreference", "home.title", "home.subtitle", "home.description", "home.getStarted", "home.learnMore", "home.features.title", "home.features.performance.title", "home.features.performance.description", "home.features.scalable.title", "home.features.scalable.description", "home.features.secure.title", "home.features.secure.description", "themeDemo.title", "themeDemo.description", "themeDemo.chineseTest", "themeDemo.englishTest", "themeDemo.chineseDescription", "themeDemo.englishDescription", "themeDemo.chineseInput", "themeDemo.englishInput", "themeDemo.chinesePlaceholder", "themeDemo.englishPlaceholder", "themeDemo.primaryButton", "themeDemo.secondaryButton", "themeDemo.outlineButton", "instructions.getStarted", "instructions.saveChanges", "actions.deployNow", "actions.readDocs", "footer.learn", "footer.examples", "footer.goToNextjs", "formatting.date.today", "formatting.date.yesterday", "formatting.date.tomorrow", "formatting.date.lastUpdated", "formatting.date.publishedOn", "formatting.number.currency", "formatting.number.percentage", "formatting.number.fileSize", "formatting.plurals.items.zero", "formatting.plurals.items.one", "formatting.plurals.items.other", "formatting.plurals.users.zero", "formatting.plurals.users.one", "formatting.plurals.users.other", "formatting.plurals.notifications.zero", "formatting.plurals.notifications.one", "formatting.plurals.notifications.other", "errors.notFound", "errors.serverError", "errors.networkError", "errors.validationError", "errors.unauthorized", "errors.forbidden", "errors.timeout", "errors.generic", "errors.translationMissing", "errors.loadingFailed", "accessibility.skipToContent", "accessibility.openMenu", "accessibility.closeMenu", "accessibility.loading", "accessibility.error", "accessibility.languageSelector", "accessibility.themeSelector", "seo.title", "seo.description", "seo.siteName", "seo.keywords", "structured-data.organization.name", "structured-data.organization.description", "structured-data.organization.phone", "structured-data.organization.social.twitter", "structured-data.organization.social.linkedin", "structured-data.organization.social.github", "structured-data.website.name", "structured-data.website.description", "structured-data.article.defaultTitle", "structured-data.article.defaultDescription", "structured-data.article.defaultAuthor", "structured-data.product.defaultName", "structured-data.product.defaultDescription"]}, "errors": [], "warnings": []}