{"timestamp": "2025-08-02T07:36:03.338Z", "deployment": {"ready": false, "score": 75, "blockers": [], "warnings": [{"type": "security", "severity": "medium", "message": "发现轻微安全问题", "action": "建议在下次更新中修复"}, {"type": "performance", "severity": "medium", "message": "性能指标超出建议值", "action": "考虑优化性能后部署"}, {"type": "quality", "severity": "low", "message": "代码质量有改进空间", "action": "建议修复质量问题"}]}, "checks": {"build": {"status": "PASS", "message": "构建验证成功"}, "tests": {"status": "PASS", "message": "所有测试通过"}, "security": {"status": "WARN", "message": "发现轻微安全问题: Command failed: pnpm security:audit..."}, "performance": {"status": "WARN", "message": "性能问题: Command failed: pnpm perf:check\n- Adding to empty webpack project\n✔ Adding to empty webpack project\n- Running JS in headless Chrome\n✔ Running JS in headless Chrome\n..."}, "quality": {"status": "WARN", "message": "质量问题: Command failed: pnpm quality:quick:verbose..."}}}