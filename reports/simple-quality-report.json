{"timestamp": "2025-08-02T07:19:02.551Z", "project": "tucsenberg-web-frontier", "mode": "lightweight", "summary": {"overallScore": 50, "totalChecks": 3, "passedChecks": 1, "failedChecks": 2, "skippedChecks": 0}, "checks": [{"name": "TypeScript类型检查", "command": "pnpm type-check", "status": "FAIL", "error": "Command failed: pnpm type-check", "timestamp": "2025-08-02T07:19:04.528Z"}, {"name": "Prettier格式检查", "command": "pnpm format:check", "status": "FAIL", "error": "Command failed: pnpm format:check\n[warn] docs/data/memory/tasks_memory_2025-08-02T07-08-47.json\n[warn] docs/data/tasks.json\n[warn] jest.setup.js\n[warn] quality-report.json\n[warn] quality-report.md\n[warn] scripts/quick-quality-check.js\n[warn] scripts/simple-quality-report.js\n[warn] src/lib/__tests__/accessibility.test.ts\n[warn] Code style issues found in 8 files. Run Prettier with --write to fix.\n", "timestamp": "2025-08-02T07:19:09.229Z"}, {"name": "单元测试", "command": "pnpm test", "status": "PASS", "output": "> tucsenberg-web-frontier@0.1.0 test /Users/<USER>/Warehouse/Focus/tucsenberg-web-frontier\n> jest --passWithNoTests", "timestamp": "2025-08-02T07:19:14.130Z"}], "recommendations": [{"priority": "HIGH", "category": "质量问题", "title": "修复失败的质量检查", "description": "发现 2 个质量问题需要修复", "action": "查看详细错误信息并逐一修复"}], "stats": {"totalFiles": 71, "testFiles": 5, "codeLines": 14444, "testCoverage": 85}}