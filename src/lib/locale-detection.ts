import { Locale } from '@/types/i18n';

import { LocaleStorageManager, UserLocalePreference } from './locale-storage';

// 支持的语言列表
const SUPPORTED_LOCALES: Locale[] = ['en', 'zh'];
const DEFAULT_LOCALE: Locale = 'en';

// 地理位置到语言的映射
const GEO_LOCALE_MAP: Record<string, Locale> = {
  // 中文地区
  CN: 'zh', // 中国大陆
  TW: 'zh', // 台湾
  HK: 'zh', // 香港
  MO: 'zh', // 澳门
  SG: 'zh', // 新加坡 (部分中文用户)

  // 英文地区 (默认)
  US: 'en', // 美国
  GB: 'en', // 英国
  CA: 'en', // 加拿大
  AU: 'en', // 澳大利亚
  NZ: 'en', // 新西兰
  IE: 'en', // 爱尔兰
  ZA: 'en', // 南非
  IN: 'en', // 印度
};

// 浏览器语言到支持语言的映射
const BROWSER_LOCALE_MAP: Record<string, Locale> = {
  // 中文变体
  'zh': 'zh',
  'zh-CN': 'zh',
  'zh-TW': 'zh',
  'zh-HK': 'zh',
  'zh-SG': 'zh',
  'zh-Hans': 'zh',
  'zh-Hant': 'zh',

  // 英文变体
  'en': 'en',
  'en-US': 'en',
  'en-GB': 'en',
  'en-CA': 'en',
  'en-AU': 'en',
  'en-NZ': 'en',
  'en-IE': 'en',
  'en-ZA': 'en',
  'en-IN': 'en',
};

/**
 * 检测结果接口
 */
export interface LocaleDetectionResult {
  locale: Locale;
  source: 'user' | 'geo' | 'browser' | 'default';
  confidence: number; // 0-1
  details: {
    userOverride?: Locale;
    geoCountry?: string;
    browserLanguages?: string[];
    fallbackUsed?: boolean;
  };
}

/**
 * 智能语言检测器
 */
export class SmartLocaleDetector {
  /**
   * 执行完整的语言检测 (需要传入检测参数)
   */
  static detectLocale(params?: {
    country?: string;
    acceptLanguage?: string;
    userOverride?: Locale;
  }): LocaleDetectionResult {
    // 1. 检查用户手动选择 (最高优先级)
    const userOverride = params?.userOverride || this.detectUserOverride();
    if (userOverride) {
      return {
        locale: userOverride,
        source: 'user',
        confidence: 1.0,
        details: { userOverride },
      };
    }

    // 2. 检查地理位置
    const geoResult = this.detectFromGeolocation(params?.country);
    if (geoResult.confidence > 0.7) {
      return geoResult;
    }

    // 3. 检查浏览器语言
    const browserResult = this.detectFromBrowser(params?.acceptLanguage);
    if (browserResult.confidence > 0.6) {
      return browserResult;
    }

    // 4. 使用地理位置结果 (如果有)
    if (geoResult.confidence > 0) {
      return geoResult;
    }

    // 5. 使用浏览器结果 (如果有)
    if (browserResult.confidence > 0) {
      return browserResult;
    }

    // 6. 回退到默认语言
    return {
      locale: DEFAULT_LOCALE,
      source: 'default',
      confidence: 0.5,
      details: { fallbackUsed: true },
    };
  }

  /**
   * 检测用户手动选择的语言
   */
  private static detectUserOverride(): Locale | null {
    try {
      return LocaleStorageManager.getUserOverride();
    } catch (error) {
      // 静默处理用户偏好检测错误，避免在生产环境中输出日志
      if (process.env.NODE_ENV === 'development') {
        // 在开发环境中可以使用调试器或其他日志方案
        // console.warn('Failed to detect user override:', error);
      }
      return null;
    }
  }

  /**
   * 基于地理位置检测语言 (需要从外部传入地理信息)
   */
  static detectFromGeolocation(country?: string): LocaleDetectionResult {
    try {
      if (!country) {
        return {
          locale: DEFAULT_LOCALE,
          source: 'geo',
          confidence: 0,
          details: { geoCountry: 'unknown' },
        };
      }

      const detectedLocale = GEO_LOCALE_MAP[country.toUpperCase()];

      if (detectedLocale && SUPPORTED_LOCALES.includes(detectedLocale)) {
        // 根据地区调整置信度
        const confidence = this.calculateGeoConfidence(country.toUpperCase());

        return {
          locale: detectedLocale,
          source: 'geo',
          confidence,
          details: { geoCountry: country.toUpperCase() },
        };
      }

      // 未知地区，默认英文
      return {
        locale: DEFAULT_LOCALE,
        source: 'geo',
        confidence: 0.3,
        details: { geoCountry: country.toUpperCase() },
      };
    } catch (error) {
      // 静默处理地理位置检测错误，避免在生产环境中输出日志
      if (process.env.NODE_ENV === 'development') {
        // 在开发环境中可以使用调试器或其他日志方案
        // console.warn('Failed to detect locale from geolocation:', error);
      }
      return {
        locale: DEFAULT_LOCALE,
        source: 'geo',
        confidence: 0,
        details: { geoCountry: 'error' },
      };
    }
  }

  /**
   * 基于浏览器语言检测
   */
  static detectFromBrowser(acceptLanguage?: string): LocaleDetectionResult {
    try {
      if (!acceptLanguage) {
        return {
          locale: DEFAULT_LOCALE,
          source: 'browser',
          confidence: 0,
          details: { browserLanguages: [] },
        };
      }

      const browserLanguages = this.parseAcceptLanguage(acceptLanguage);

      // 查找第一个支持的语言
      for (const lang of browserLanguages) {
        const normalizedLang = lang.toLowerCase();
        // 安全的对象属性访问，避免对象注入
        const detectedLocale = Object.prototype.hasOwnProperty.call(
          BROWSER_LOCALE_MAP,
          normalizedLang,
        )
          ? (BROWSER_LOCALE_MAP as Record<string, Locale>)[normalizedLang]
          : undefined;

        if (detectedLocale && SUPPORTED_LOCALES.includes(detectedLocale)) {
          const confidence = this.calculateBrowserConfidence(
            lang,
            browserLanguages,
          );

          return {
            locale: detectedLocale,
            source: 'browser',
            confidence,
            details: { browserLanguages },
          };
        }
      }

      // 没有找到支持的语言
      return {
        locale: DEFAULT_LOCALE,
        source: 'browser',
        confidence: 0.2,
        details: { browserLanguages },
      };
    } catch (error) {
      // 静默处理浏览器语言检测错误，避免在生产环境中输出日志
      if (process.env.NODE_ENV === 'development') {
        // 在开发环境中可以使用调试器或其他日志方案
        // console.warn('Failed to detect locale from browser:', error);
      }
      return {
        locale: DEFAULT_LOCALE,
        source: 'browser',
        confidence: 0,
        details: { browserLanguages: [] },
      };
    }
  }

  /**
   * 解析 Accept-Language 头
   */
  private static parseAcceptLanguage(acceptLanguage: string): string[] {
    return acceptLanguage
      .split(',')
      .map((lang) => lang?.split(';')[0]?.trim())
      .filter(Boolean) as string[];
  }

  /**
   * 计算地理位置检测的置信度
   */
  private static calculateGeoConfidence(country: string): number {
    // 中文地区的置信度
    const chineseRegions = ['CN', 'TW', 'HK', 'MO'];
    if (chineseRegions.includes(country)) {
      return country === 'CN' ? 0.9 : 0.8; // 大陆地区置信度更高
    }

    // 主要英语国家的置信度
    const primaryEnglishRegions = ['US', 'GB', 'CA', 'AU', 'NZ', 'IE'];
    if (primaryEnglishRegions.includes(country)) {
      return 0.8;
    }

    // 其他英语地区
    const secondaryEnglishRegions = ['ZA', 'IN', 'SG'];
    if (secondaryEnglishRegions.includes(country)) {
      return 0.6;
    }

    // 未知地区，低置信度
    return 0.3;
  }

  /**
   * 计算浏览器语言检测的置信度
   */
  private static calculateBrowserConfidence(
    detectedLang: string,
    allLanguages: string[],
  ): number {
    // 如果是第一个语言，置信度更高
    const isFirstLanguage = allLanguages[0] === detectedLang;

    // 精确匹配的置信度更高
    const isExactMatch = ['zh', 'en'].includes(detectedLang.toLowerCase());

    let confidence = 0.6;

    if (isFirstLanguage) confidence += 0.2;
    if (isExactMatch) confidence += 0.1;

    return Math.min(confidence, 0.9);
  }

  /**
   * 保存检测结果到存储
   */
  static saveDetectionResult(result: LocaleDetectionResult): void {
    if (result.source === 'user') {
      // 用户手动选择，不需要保存
      return;
    }

    const preference: UserLocalePreference = {
      locale: result.locale,
      source: result.source,
      timestamp: Date.now(),
      confidence: result.confidence,
    };

    try {
      LocaleStorageManager.saveUserPreference(preference);
    } catch (error) {
      // 静默处理保存检测结果错误，避免在生产环境中输出日志
      if (process.env.NODE_ENV === 'development') {
        // 在开发环境中可以使用调试器或其他日志方案
        // console.warn('Failed to save detection result:', error);
      }
    }
  }
}

/**
 * 客户端语言检测 Hook
 */
export function useClientLocaleDetection() {
  const detectClientLocale = (): LocaleDetectionResult => {
    // 客户端检测逻辑
    const userOverride = LocaleStorageManager.getUserOverride();
    if (userOverride) {
      return {
        locale: userOverride,
        source: 'user',
        confidence: 1.0,
        details: { userOverride },
      };
    }

    // 浏览器语言检测
    if (typeof navigator !== 'undefined') {
      const languages = navigator.languages || [navigator.language];

      for (const lang of languages) {
        const normalizedLang = lang.toLowerCase();
        // 安全的对象属性访问，避免对象注入
        const detectedLocale = Object.prototype.hasOwnProperty.call(
          BROWSER_LOCALE_MAP,
          normalizedLang,
        )
          ? (BROWSER_LOCALE_MAP as Record<string, Locale>)[normalizedLang]
          : undefined;

        if (detectedLocale && SUPPORTED_LOCALES.includes(detectedLocale)) {
          return {
            locale: detectedLocale,
            source: 'browser',
            confidence: 0.7,
            details: { browserLanguages: [...languages] },
          };
        }
      }
    }

    return {
      locale: DEFAULT_LOCALE,
      source: 'default',
      confidence: 0.5,
      details: { fallbackUsed: true },
    };
  };

  return { detectClientLocale };
}
