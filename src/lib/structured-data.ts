import { getTranslations } from 'next-intl/server';

import { I18nPerformanceMonitor } from '@/lib/i18n-performance';

import { routing } from '@/i18n/routing';

export type Locale = 'en' | 'zh';

// 严格的结构化数据接口定义
interface OrganizationData {
  name?: string;
  description?: string;
  url?: string;
  logo?: string;
  phone?: string;
  email?: string;
  address?: string;
}

interface WebSiteData {
  name?: string;
  description?: string;
  url?: string;
  searchUrl?: string;
}

interface ArticleData {
  title: string;
  description: string;
  author?: string;
  publishedTime: string;
  modifiedTime?: string;
  url: string;
  image?: string;
  section?: string;
}

interface ProductData {
  name: string;
  description: string;
  brand?: string;
  manufacturer?: string;
  image?: string;
  price?: number;
  currency?: string;
  availability?: string;
  sku?: string;
}

interface BreadcrumbData {
  items: Array<{
    name: string;
    url: string;
    position: number;
  }>;
}

/**
 * 生成组织结构化数据
 */
function generateOrganizationData(
  t: Awaited<ReturnType<typeof getTranslations>>,
  data: OrganizationData = {},
) {
  return {
    '@context': 'https://schema.org',
    '@type': 'Organization',
    'name':
      data.name ||
      t('organization.name', { defaultValue: 'Tucsenberg Web Frontier' }),
    'description':
      data.description ||
      t('organization.description', {
        defaultValue: 'Modern B2B Enterprise Web Platform',
      }),
    'url': data.url || process.env['SITE_URL'] || 'https://tucsenberg.com',
    'logo':
      data.logo ||
      `${process.env['SITE_URL'] || 'https://tucsenberg.com'}/logo.png`,
    'contactPoint': {
      '@type': 'ContactPoint',
      'telephone':
        data.phone || t('organization.phone', { defaultValue: '******-0123' }),
      'contactType': 'customer service',
      'availableLanguage': routing.locales,
    },
    'sameAs': [
      t('organization.social.twitter', {
        defaultValue: 'https://twitter.com/tucsenberg',
      }),
      t('organization.social.linkedin', {
        defaultValue: 'https://linkedin.com/company/tucsenberg',
      }),
      t('organization.social.github', {
        defaultValue: 'https://github.com/tucsenberg',
      }),
    ],
    // 移除 ...data 扩展运算符，只使用已验证的属性
  };
}

/**
 * 生成网站结构化数据
 */
function generateWebSiteData(
  t: Awaited<ReturnType<typeof getTranslations>>,
  data: WebSiteData = {},
) {
  return {
    '@context': 'https://schema.org',
    '@type': 'WebSite',
    'name':
      data.name ||
      t('website.name', { defaultValue: 'Tucsenberg Web Frontier' }),
    'description':
      data.description ||
      t('website.description', {
        defaultValue: 'Modern B2B Enterprise Web Platform with Next.js 15',
      }),
    'url': data.url || process.env['SITE_URL'] || 'https://tucsenberg.com',
    'potentialAction': {
      '@type': 'SearchAction',
      'target':
        data.searchUrl ||
        `${process.env['SITE_URL'] || 'https://tucsenberg.com'}/search?q={search_term_string}`,
      'query-input': 'required name=search_term_string',
    },
    'inLanguage': routing.locales,
    // 移除 ...data 扩展运算符，只使用已验证的属性
  };
}

/**
 * 生成文章结构化数据
 */
function generateArticleData(
  t: Awaited<ReturnType<typeof getTranslations>>,
  locale: Locale,
  data: ArticleData,
) {
  return {
    '@context': 'https://schema.org',
    '@type': 'Article',
    'headline': data.title,
    'description': data.description,
    'author': {
      '@type': 'Person',
      'name':
        data.author ||
        t('article.defaultAuthor', { defaultValue: 'Tucsenberg Team' }),
    },
    'publisher': {
      '@type': 'Organization',
      'name': t('organization.name', {
        defaultValue: 'Tucsenberg Web Frontier',
      }),
      'logo': {
        '@type': 'ImageObject',
        'url': `${process.env['SITE_URL'] || 'https://tucsenberg.com'}/logo.png`,
      },
    },
    'datePublished': data.publishedTime,
    'dateModified': data.modifiedTime || data.publishedTime,
    'mainEntityOfPage': {
      '@type': 'WebPage',
      '@id': data.url,
    },
    'image': data.image
      ? {
          '@type': 'ImageObject',
          'url': data.image,
        }
      : undefined,
    'inLanguage': locale,
    'section': data.section,
    // 移除 ...data 扩展运算符，只使用已验证的属性
  };
}

/**
 * 生成产品结构化数据
 */
function generateProductData(
  t: Awaited<ReturnType<typeof getTranslations>>,
  data: ProductData,
) {
  return {
    '@context': 'https://schema.org',
    '@type': 'Product',
    'name': data.name,
    'description': data.description,
    'brand': {
      '@type': 'Brand',
      'name':
        data.brand ||
        t('organization.name', {
          defaultValue: 'Tucsenberg Web Frontier',
        }),
    },
    'manufacturer': {
      '@type': 'Organization',
      'name':
        data.manufacturer ||
        t('organization.name', {
          defaultValue: 'Tucsenberg Web Frontier',
        }),
    },
    'image': data.image ? [data.image] : undefined,
    'offers': data.price
      ? {
          '@type': 'Offer',
          'price': data.price,
          'priceCurrency': data.currency || 'USD',
          'availability': data.availability || 'https://schema.org/InStock',
        }
      : undefined,
    'sku': data.sku,
    // 移除 ...data 扩展运算符，只使用已验证的属性
  };
}

/**
 * 生成面包屑结构化数据
 */
function generateBreadcrumbData(data: BreadcrumbData) {
  return {
    '@context': 'https://schema.org',
    '@type': 'BreadcrumbList',
    'itemListElement':
      data.items?.map((item, index) => ({
        '@type': 'ListItem',
        'position': item.position || index + 1,
        'name': item.name,
        'item': item.url,
      })) || [],
    // 移除 ...data 扩展运算符，只使用已验证的属性
  };
}

// 联合类型定义
type StructuredDataType =
  | OrganizationData
  | WebSiteData
  | ArticleData
  | ProductData
  | BreadcrumbData;

/**
 * 生成本地化结构化数据
 */
export async function generateLocalizedStructuredData(
  locale: Locale,
  type: 'Organization' | 'WebSite' | 'Article' | 'Product' | 'BreadcrumbList',
  data: StructuredDataType,
): Promise<Record<string, unknown>> {
  try {
    // 使用原始的getTranslations，缓存已在底层实现
    const t = await getTranslations({ locale, namespace: 'structured-data' });

    switch (type) {
      case 'Organization':
        return generateOrganizationData(t, data as OrganizationData);
      case 'WebSite':
        return generateWebSiteData(t, data as WebSiteData);
      case 'Article':
        return generateArticleData(t, locale, data as ArticleData);
      case 'Product':
        return generateProductData(t, data as ProductData);
      case 'BreadcrumbList':
        return generateBreadcrumbData(data as BreadcrumbData);
      default:
        // 对于未知类型，返回基础结构而不使用扩展运算符
        return {
          '@context': 'https://schema.org',
          '@type': type,
        };
    }
  } catch (error) {
    // 记录错误并返回基础结构
    if (error instanceof Error) {
      // 处理已知错误类型
      I18nPerformanceMonitor.recordError();
    }
    // 错误情况下也不使用扩展运算符，避免潜在的安全风险
    return {
      '@context': 'https://schema.org',
      '@type': type,
    };
  }
}

/**
 * 生成JSON-LD脚本标签
 */
export function generateJSONLD(structuredData: unknown): string {
  const JSON_INDENT = 2;
  return JSON.stringify(structuredData, null, JSON_INDENT);
}

/**
 * 创建面包屑导航结构化数据
 */
export function createBreadcrumbStructuredData(
  locale: Locale,
  breadcrumbs: Array<{ name: string; url: string }>,
) {
  return generateLocalizedStructuredData(locale, 'BreadcrumbList', {
    items: breadcrumbs.map((item, index) => ({
      name: item.name,
      url: item.url,
      position: index + 1,
    })),
  });
}

/**
 * 创建文章结构化数据
 */
export function createArticleStructuredData(
  locale: Locale,
  article: {
    title: string;
    description: string;
    author?: string;
    publishedTime: string;
    modifiedTime?: string;
    url: string;
    image?: string;
  },
) {
  return generateLocalizedStructuredData(locale, 'Article', article);
}
