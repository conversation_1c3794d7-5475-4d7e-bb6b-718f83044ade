import { createNavigation } from 'next-intl/navigation';
import { defineRouting } from 'next-intl/routing';

import { getRoutingConfig, validatePathsConfig } from '@/config/paths';

// 验证路径配置
const validation = validatePathsConfig();
if (!validation.isValid) {
  // eslint-disable-next-line no-console
  console.error('路径配置验证失败:', validation.errors);
  throw new Error('路径配置不一致，请检查 src/config/paths.ts');
}

// 使用统一配置创建路由
const routingConfig = getRoutingConfig();

export const routing = defineRouting({
  ...routingConfig,

  // 启用hreflang链接
  alternateLinks: true,

  // 启用智能语言检测
  localeDetection: true,
});

// Lightweight wrappers around Next.js' navigation APIs
// that will consider the routing configuration
export const { Link, redirect, usePathname, useRouter } =
  createNavigation(routing);

// 导出类型，使用统一配置
export type Locale = (typeof routing.locales)[number];

// 导出配置验证函数，供其他模块使用
export { validatePathsConfig } from '@/config/paths';
