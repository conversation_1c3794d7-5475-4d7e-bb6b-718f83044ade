import { NextResponse } from 'next/server';

import { getAllPages, getAllPosts, getContentStats } from '@/lib/content';

export async function GET() {
  try {
    // Test content management system
    const enPosts = getAllPosts('en');
    const zhPosts = getAllPosts('zh');
    const enPages = getAllPages('en');
    const zhPages = getAllPages('zh');
    const stats = getContentStats();

    const result = {
      success: true,
      message: 'Content management system is working!',
      data: {
        posts: {
          en: enPosts.length,
          zh: zhPosts.length,
          total: enPosts.length + zhPosts.length,
          examples: {
            en:
              enPosts.length > 0
                ? {
                    title: enPosts[0]?.metadata?.title,
                    slug: enPosts[0]?.metadata?.slug,
                    publishedAt: enPosts[0]?.metadata?.publishedAt,
                  }
                : null,
            zh:
              zhPosts.length > 0
                ? {
                    title: zhPosts[0]?.metadata?.title,
                    slug: zhPosts[0]?.metadata?.slug,
                    publishedAt: zhPosts[0]?.metadata?.publishedAt,
                  }
                : null,
          },
        },
        pages: {
          en: enPages.length,
          zh: zhPages.length,
          total: enPages.length + zhPages.length,
          examples: {
            en:
              enPages.length > 0
                ? {
                    title: enPages[0]?.metadata?.title,
                    slug: enPages[0]?.metadata?.slug,
                  }
                : null,
            zh:
              zhPages.length > 0
                ? {
                    title: zhPages[0]?.metadata?.title,
                    slug: zhPages[0]?.metadata?.slug,
                  }
                : null,
          },
        },
        stats,
        features: {
          mdxParsing: true,
          frontmatterValidation: true,
          multiLanguageSupport: true,
          typeScriptTypes: true,
          contentValidation: true,
          gitBasedWorkflow: true,
        },
      },
    };

    return NextResponse.json(result);
  } catch (error) {
    console.error('Content test error:', error);
    return NextResponse.json(
      {
        success: false,
        message: 'Content management system test failed',
        error: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 },
    );
  }
}
