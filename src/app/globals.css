@import 'tailwindcss';
@import 'tw-animate-css';

@custom-variant dark (&:is(.dark *));

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
  --color-sidebar-ring: var(--sidebar-ring);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar: var(--sidebar);
  --color-chart-5: var(--chart-5);
  --color-chart-4: var(--chart-4);
  --color-chart-3: var(--chart-3);
  --color-chart-2: var(--chart-2);
  --color-chart-1: var(--chart-1);
  --color-ring: var(--ring);
  --color-input: var(--input);
  --color-border: var(--border);
  --color-destructive: var(--destructive);
  --color-accent-foreground: var(--accent-foreground);
  --color-accent: var(--accent);
  --color-muted-foreground: var(--muted-foreground);
  --color-muted: var(--muted);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-secondary: var(--secondary);
  --color-primary-foreground: var(--primary-foreground);
  --color-primary: var(--primary);
  --color-popover-foreground: var(--popover-foreground);
  --color-popover: var(--popover);
  --color-card-foreground: var(--card-foreground);
  --color-card: var(--card);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
}

:root {
  --radius: 0.625rem;

  /* 基础颜色 - 优化对比度确保WCAG AA级合规 */
  --background: oklch(1 0 0);
  --foreground: oklch(0.145 0 0);

  /* 卡片和弹出层 */
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.145 0 0);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.145 0 0);

  /* 主要颜色 */
  --primary: oklch(0.205 0 0);
  --primary-foreground: oklch(0.985 0 0);

  /* 次要颜色 */
  --secondary: oklch(0.97 0 0);
  --secondary-foreground: oklch(0.205 0 0);

  /* 静音颜色 */
  --muted: oklch(0.97 0 0);
  --muted-foreground: oklch(0.556 0 0);

  /* 强调颜色 */
  --accent: oklch(0.97 0 0);
  --accent-foreground: oklch(0.205 0 0);

  /* 破坏性颜色 */
  --destructive: oklch(0.577 0.245 27.325);
  --destructive-foreground: oklch(0.985 0 0);

  /* 边框和输入 */
  --border: oklch(0.922 0 0);
  --input: oklch(0.922 0 0);
  --ring: oklch(0.708 0 0);

  /* 语义化颜色 - 新增 */
  --success: oklch(0.646 0.222 142.5);
  --success-foreground: oklch(0.985 0 0);
  --warning: oklch(0.828 0.189 84.429);
  --warning-foreground: oklch(0.145 0 0);
  --error: oklch(0.577 0.245 27.325);
  --error-foreground: oklch(0.985 0 0);
  --info: oklch(0.6 0.118 184.704);
  --info-foreground: oklch(0.985 0 0);

  /* 图表颜色 - 保持向后兼容 */
  --chart-1: oklch(0.646 0.222 41.116);
  --chart-2: oklch(0.6 0.118 184.704);
  --chart-3: oklch(0.398 0.07 227.392);
  --chart-4: oklch(0.828 0.189 84.429);
  --chart-5: oklch(0.769 0.188 70.08);

  /* 侧边栏颜色 */
  --sidebar: oklch(0.985 0 0);
  --sidebar-foreground: oklch(0.145 0 0);
  --sidebar-primary: oklch(0.205 0 0);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.97 0 0);
  --sidebar-accent-foreground: oklch(0.205 0 0);
  --sidebar-border: oklch(0.922 0 0);
  --sidebar-ring: oklch(0.708 0 0);
}

.dark {
  /* 基础颜色 - 暗黑主题优化 */
  --background: oklch(0.145 0 0);
  --foreground: oklch(0.985 0 0);

  /* 卡片和弹出层 */
  --card: oklch(0.205 0 0);
  --card-foreground: oklch(0.985 0 0);
  --popover: oklch(0.205 0 0);
  --popover-foreground: oklch(0.985 0 0);

  /* 主要颜色 */
  --primary: oklch(0.922 0 0);
  --primary-foreground: oklch(0.205 0 0);

  /* 次要颜色 */
  --secondary: oklch(0.269 0 0);
  --secondary-foreground: oklch(0.985 0 0);

  /* 静音颜色 */
  --muted: oklch(0.269 0 0);
  --muted-foreground: oklch(0.708 0 0);

  /* 强调颜色 */
  --accent: oklch(0.269 0 0);
  --accent-foreground: oklch(0.985 0 0);

  /* 破坏性颜色 */
  --destructive: oklch(0.704 0.191 22.216);
  --destructive-foreground: oklch(0.985 0 0);

  /* 边框和输入 */
  --border: oklch(1 0 0 / 10%);
  --input: oklch(1 0 0 / 15%);
  --ring: oklch(0.556 0 0);

  /* 语义化颜色 - 暗黑主题版本 */
  --success: oklch(0.7 0.222 142.5);
  --success-foreground: oklch(0.145 0 0);
  --warning: oklch(0.85 0.189 84.429);
  --warning-foreground: oklch(0.145 0 0);
  --error: oklch(0.704 0.191 22.216);
  --error-foreground: oklch(0.985 0 0);
  --info: oklch(0.65 0.118 184.704);
  --info-foreground: oklch(0.145 0 0);

  /* 图表颜色 - 暗黑主题版本 */
  --chart-1: oklch(0.488 0.243 264.376);
  --chart-2: oklch(0.696 0.17 162.48);
  --chart-3: oklch(0.769 0.188 70.08);
  --chart-4: oklch(0.627 0.265 303.9);
  --chart-5: oklch(0.645 0.246 16.439);

  /* 侧边栏颜色 - 暗黑主题版本 */
  --sidebar: oklch(0.205 0 0);
  --sidebar-foreground: oklch(0.985 0 0);
  --sidebar-primary: oklch(0.488 0.243 264.376);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.269 0 0);
  --sidebar-accent-foreground: oklch(0.985 0 0);
  --sidebar-border: oklch(1 0 0 / 10%);
  --sidebar-ring: oklch(0.556 0 0);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
    font-family:
      var(--font-geist-sans), 'PingFang SC', 'Hiragino Sans GB',
      'Microsoft YaHei', sans-serif;
  }

  /* 中文字体优化 */
  .font-chinese {
    font-family:
      'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei',
      var(--font-geist-sans), sans-serif;
  }

  /* B2B企业级设计风格 */
  .enterprise-card {
    @apply bg-card border-border rounded-lg border shadow-sm;
  }

  .enterprise-header {
    @apply text-foreground mb-4 text-2xl font-semibold;
  }

  .enterprise-text {
    @apply text-muted-foreground leading-relaxed;
  }

  /* View Transitions API 支持 */
  @supports (view-transition-name: none) {
    /* 为根元素设置view-transition-name */
    :root {
      view-transition-name: root;
    }

    /* 主题切换动画优化 */
    ::view-transition-old(root),
    ::view-transition-new(root) {
      animation-duration: 0.4s;
      animation-timing-function: ease-in-out;
    }

    /* 减少动画偏好支持 */
    @media (prefers-reduced-motion: reduce) {
      ::view-transition-old(root),
      ::view-transition-new(root) {
        animation-duration: 0.1s;
      }
    }

    /* 高对比度模式支持 */
    @media (prefers-contrast: high) {
      ::view-transition-old(root),
      ::view-transition-new(root) {
        animation-timing-function: linear;
      }
    }
  }

  /* 全局减少动画偏好支持 */
  @media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
      animation-duration: 0.01ms !important;
      animation-iteration-count: 1 !important;
      transition-duration: 0.01ms !important;
      scroll-behavior: auto !important;
    }
  }

  /* 高对比度模式增强 */
  @media (prefers-contrast: high) {
    :root {
      --border: oklch(0 0 0);
      --ring: oklch(0 0 0);
    }

    .dark {
      --border: oklch(1 0 0);
      --ring: oklch(1 0 0);
    }

    /* 增强焦点指示器 */
    *:focus-visible {
      outline: 3px solid var(--ring) !important;
      outline-offset: 2px !important;
    }

    /* 增强按钮边框 */
    button,
    [role='button'] {
      border-width: 2px !important;
    }
  }

  /* 无障碍性增强 */
  .sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
  }

  /* 焦点管理 */
  *:focus-visible {
    outline: 2px solid var(--ring);
    outline-offset: 2px;
  }

  /* 跳过链接 */
  .skip-link {
    position: absolute;
    top: -40px;
    left: 6px;
    background: var(--background);
    color: var(--foreground);
    padding: 8px;
    text-decoration: none;
    border-radius: 4px;
    border: 2px solid var(--border);
    z-index: 1000;
  }

  .skip-link:focus {
    top: 6px;
  }
}
