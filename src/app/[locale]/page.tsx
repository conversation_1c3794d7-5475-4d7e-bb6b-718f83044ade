import Image from 'next/image';

import { useTranslations } from 'next-intl';

import { LanguageToggle } from '@/components/language-toggle';
import { ThemeToggle } from '@/components/theme-toggle';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';

// Components
function Header() {
  return (
    <div className='mb-8 flex w-full items-center justify-between'>
      <Image
        className='dark:invert'
        src='/next.svg'
        alt='Next.js logo'
        width={180}
        height={38}
        priority
      />
      <div className='flex items-center gap-2'>
        <LanguageToggle />
        <ThemeToggle />
      </div>
    </div>
  );
}

function Instructions() {
  return (
    <ol className='list-inside list-decimal text-center font-mono text-sm/6 sm:text-left'>
      <li className='mb-2 tracking-[-.01em]'>
        Get started by editing{' '}
        <code className='rounded bg-black/[.05] px-1 py-0.5 font-mono font-semibold dark:bg-white/[.06]'>
          src/app/page.tsx
        </code>
        .
      </li>
      <li className='tracking-[-.01em]'>
        Save and see your changes instantly.
      </li>
    </ol>
  );
}

function ActionButtons() {
  return (
    <div className='flex flex-col items-center gap-4 sm:flex-row'>
      <Button asChild>
        <a
          href='https://vercel.com/new?utm_source=create-next-app&utm_medium=appdir-template-tw&utm_campaign=create-next-app'
          target='_blank'
          rel='noopener noreferrer'
        >
          <Image
            className='dark:invert'
            src='/vercel.svg'
            alt='Vercel logomark'
            width={20}
            height={20}
            style={{ width: 'auto', height: 'auto' }}
          />
          Deploy now
        </a>
      </Button>
      <Button
        variant='outline'
        asChild
      >
        <a
          href='https://nextjs.org/docs?utm_source=create-next-app&utm_medium=appdir-template-tw&utm_campaign=create-next-app'
          target='_blank'
          rel='noopener noreferrer'
        >
          Read our docs
        </a>
      </Button>
    </div>
  );
}

function ThemeSystemDemo() {
  const t = useTranslations('themeDemo');

  return (
    <div className='w-full max-w-4xl space-y-6'>
      <Card className='enterprise-card'>
        <CardHeader>
          <CardTitle className='enterprise-header'>{t('title')}</CardTitle>
          <CardDescription className='enterprise-text'>
            {t('description')}
          </CardDescription>
        </CardHeader>
        <CardContent className='space-y-6'>
          <div className='grid grid-cols-1 gap-6 md:grid-cols-2'>
            <div className='space-y-4'>
              <h3 className='text-lg font-semibold'>{t('chineseTest')}</h3>
              <p className='font-chinese enterprise-text'>
                {t('chineseDescription')}
              </p>
              <div className='space-y-2'>
                <Label htmlFor='chinese-input'>{t('chineseInput')}</Label>
                <Input
                  id='chinese-input'
                  placeholder={t('chinesePlaceholder')}
                />
              </div>
            </div>

            <div className='space-y-4'>
              <h3 className='text-lg font-semibold'>{t('englishTest')}</h3>
              <p className='enterprise-text'>{t('englishDescription')}</p>
              <div className='space-y-2'>
                <Label htmlFor='english-input'>{t('englishInput')}</Label>
                <Input
                  id='english-input'
                  placeholder={t('englishPlaceholder')}
                />
              </div>
            </div>
          </div>

          <div className='flex flex-wrap gap-3'>
            <Button>{t('primaryButton')}</Button>
            <Button variant='secondary'>{t('secondaryButton')}</Button>
            <Button variant='outline'>{t('outlineButton')}</Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

function Footer() {
  return (
    <footer className='row-start-3 flex flex-wrap items-center justify-center gap-[24px]'>
      <a
        className='flex items-center gap-2 hover:underline hover:underline-offset-4'
        href='https://nextjs.org/learn?utm_source=create-next-app&utm_medium=appdir-template-tw&utm_campaign=create-next-app'
        target='_blank'
        rel='noopener noreferrer'
      >
        <Image
          aria-hidden
          src='/file.svg'
          alt='File icon'
          width={16}
          height={16}
        />
        Learn
      </a>
      <a
        className='flex items-center gap-2 hover:underline hover:underline-offset-4'
        href='https://vercel.com/templates?framework=next.js&utm_source=create-next-app&utm_medium=appdir-template-tw&utm_campaign=create-next-app'
        target='_blank'
        rel='noopener noreferrer'
      >
        <Image
          aria-hidden
          src='/window.svg'
          alt='Window icon'
          width={16}
          height={16}
        />
        Examples
      </a>
      <a
        className='flex items-center gap-2 hover:underline hover:underline-offset-4'
        href='https://nextjs.org?utm_source=create-next-app&utm_medium=appdir-template-tw&utm_campaign=create-next-app'
        target='_blank'
        rel='noopener noreferrer'
      >
        <Image
          aria-hidden
          src='/globe.svg'
          alt='Globe icon'
          width={16}
          height={16}
        />
        Go to nextjs.org →
      </a>
    </footer>
  );
}

export default function Home() {
  const t = useTranslations('home');

  return (
    <div className='bg-background text-foreground min-h-screen'>
      <div className='container mx-auto px-4 py-8'>
        <Header />

        <main className='flex flex-col items-center gap-12'>
          <div className='space-y-4 text-center'>
            <h1 className='text-4xl font-bold tracking-tight'>{t('title')}</h1>
            <p className='text-muted-foreground max-w-2xl text-xl'>
              {t('subtitle')}
            </p>
          </div>

          <ThemeSystemDemo />

          <div className='w-full max-w-4xl'>
            <Instructions />
          </div>

          <ActionButtons />
        </main>

        <Footer />
      </div>
    </div>
  );
}
