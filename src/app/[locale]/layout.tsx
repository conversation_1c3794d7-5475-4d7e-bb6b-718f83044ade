import type { ReactNode } from 'react';

import type { <PERSON>ada<PERSON> } from 'next';

import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mono } from 'next/font/google';
import { notFound } from 'next/navigation';

import { NextIntlClientProvider } from 'next-intl';
import { getMessages } from 'next-intl/server';

import {
  createPageSEOConfig,
  generateLocalizedMetadata,
  type Locale,
} from '@/lib/seo-metadata';
import {
  generateJSONLD,
  generateLocalizedStructuredData,
} from '@/lib/structured-data';

import { I18nPerformanceIndicator } from '@/components/i18n/performance-dashboard';
import {
  CriticalTranslationPreloader,
  PerformanceMonitoringPreloader,
} from '@/components/i18n/translation-preloader';
import { ThemeProvider } from '@/components/theme-provider';
import {
  ThemePerformanceDashboard,
  ThemePerformanceMonitor,
} from '@/components/theme/theme-performance-monitor';

import { routing } from '@/i18n/routing';

import '../globals.css';

const geistSans = Geist({
  variable: '--font-geist-sans',
  subsets: ['latin'],
});

const geistMono = Geist_Mono({
  variable: '--font-geist-mono',
  subsets: ['latin'],
});

// 动态生成元数据
export async function generateMetadata({
  params,
}: {
  params: Promise<{ locale: string }>;
}): Promise<Metadata> {
  const { locale } = await params;

  // 确保locale有效
  if (!routing.locales.includes(locale as Locale)) {
    return {
      title: 'Tucsenberg Web Frontier',
      description: 'Modern B2B Enterprise Web Platform with Next.js 15',
    };
  }

  const seoConfig = createPageSEOConfig('home');
  return generateLocalizedMetadata(locale as Locale, 'home', seoConfig);
}

interface LocaleLayoutProps {
  children: ReactNode;
  params: Promise<{ locale: string }>;
}

export default async function LocaleLayout({
  children,
  params,
}: LocaleLayoutProps) {
  const { locale } = await params;

  // Ensure that the incoming `locale` is valid
  if (!routing.locales.includes(locale as 'en' | 'zh')) {
    notFound();
  }

  // Providing all messages to the client
  // side is the easiest way to get started
  const messages = await getMessages();

  // 生成结构化数据
  const organizationData = await generateLocalizedStructuredData(
    locale as Locale,
    'Organization',
    {},
  );
  const websiteData = await generateLocalizedStructuredData(
    locale as Locale,
    'WebSite',
    {},
  );

  return (
    <html
      lang={locale}
      suppressHydrationWarning
    >
      <head>
        {/* 结构化数据 */}
        <script
          type='application/ld+json'
          dangerouslySetInnerHTML={{
            __html: generateJSONLD(organizationData),
          }}
        />
        <script
          type='application/ld+json'
          dangerouslySetInnerHTML={{
            __html: generateJSONLD(websiteData),
          }}
        />
      </head>
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased`}
      >
        <NextIntlClientProvider messages={messages}>
          <ThemeProvider
            attribute='class'
            defaultTheme='system'
            enableSystem
          >
            {/* I18n性能优化组件 */}
            <CriticalTranslationPreloader />
            <PerformanceMonitoringPreloader />

            {/* 主题性能监控组件 */}
            <ThemePerformanceMonitor />
            <ThemePerformanceDashboard />

            {children}

            {/* 开发环境性能指示器 */}
            <I18nPerformanceIndicator />
          </ThemeProvider>
        </NextIntlClientProvider>
      </body>
    </html>
  );
}
