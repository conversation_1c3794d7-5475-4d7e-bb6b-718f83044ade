---
title: 'MDX Test Page'
description: 'Testing MDX configuration and rendering'
---

# MDX Configuration Test

This page tests the MDX configuration and rendering capabilities.

## Features Tested

### 1. Basic Markdown

- **Bold text**
- _Italic text_
- `Inline code`

### 2. Code Blocks

```typescript
// TypeScript code example
interface TestInterface {
  name: string;
  value: number;
}

const testObject: TestInterface = {
  name: 'MDX Test',
  value: 42,
};
```

### 3. Lists

1. First item
2. Second item
3. Third item

- Bullet point 1
- Bullet point 2
- Bullet point 3

### 4. Blockquotes

> This is a blockquote to test the styling. It should be properly formatted with
> the custom styles.

### 5. Links

[Link to homepage](/)

### 6. Tables

| Feature     | Status | Notes   |
| ----------- | ------ | ------- |
| MDX Parsing | ✅     | Working |
| Frontmatter | ✅     | Working |
| Styling     | ✅     | Working |

---

If you can see this page properly formatted, the MDX configuration is working
correctly! 🎉
