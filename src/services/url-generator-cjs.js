/**
 * CommonJS兼容的URL生成器导出
 * 用于next-sitemap.config.js等CommonJS环境
 */

/* eslint-disable security/detect-object-injection */
/* eslint-disable no-magic-numbers */

// 硬编码配置，避免ES模块导入问题
const SITE_CONFIG = {
  baseUrl: process.env.SITE_URL || 'https://tucsenberg.com',
};

const LOCALES_CONFIG = {
  locales: ['en', 'zh'],
  defaultLocale: 'en',
  prefixes: {
    en: '',
    zh: '/zh',
  },
};

const PATHS_CONFIG = {
  home: { en: '/', zh: '/' },
  about: { en: '/about', zh: '/guanyu' },
  contact: { en: '/contact', zh: '/lianxi' },
  blog: { en: '/blog', zh: '/boke' },
  products: { en: '/products', zh: '/chanpin' },
  services: { en: '/services', zh: '/fuwu' },
  pricing: { en: '/pricing', zh: '/jiage' },
  support: { en: '/support', zh: '/zhichi' },
  privacy: { en: '/privacy', zh: '/yinsi' },
  terms: { en: '/terms', zh: '/tiaokuan' },
};

/**
 * 获取本地化路径
 */
function getLocalizedPath(pageType, locale) {
  if (!PATHS_CONFIG[pageType]) {
    throw new Error(`Unknown page type: ${pageType}`);
  }
  return PATHS_CONFIG[pageType][locale];
}

/**
 * 生成规范化URL
 */
function generateCanonicalURL(pageType, locale) {
  const localizedPath = getLocalizedPath(pageType, locale);

  if (locale === 'en') {
    return `${SITE_CONFIG.baseUrl}${localizedPath}`;
  }
  return `${SITE_CONFIG.baseUrl}/${locale}${localizedPath}`;
}

/**
 * 生成hreflang链接
 */
function generateHreflangLinks(pageType) {
  const links = [];

  LOCALES_CONFIG.locales.forEach((locale) => {
    links.push({
      href: generateCanonicalURL(pageType, locale),
      hreflang: locale,
    });
  });

  // 添加x-default链接
  links.push({
    href: generateCanonicalURL(pageType, LOCALES_CONFIG.defaultLocale),
    hreflang: 'x-default',
  });

  return links;
}

/**
 * 生成sitemap条目
 */
function generateSitemapEntry(pageType, locale, options = {}) {
  const url = generateCanonicalURL(pageType, locale);
  const alternateRefs = generateHreflangLinks(pageType);

  return {
    loc: url,
    changefreq: options.changefreq || 'weekly',
    priority: options.priority || 0.8,
    lastmod: options.lastmod || new Date().toISOString(),
    alternateRefs,
  };
}

/**
 * 生成所有页面的sitemap条目
 */
function generateAllSitemapEntries() {
  const entries = [];

  Object.keys(PATHS_CONFIG).forEach((pageType) => {
    LOCALES_CONFIG.locales.forEach((locale) => {
      const entry = generateSitemapEntry(pageType, locale, {
        changefreq: pageType === 'home' ? 'daily' : 'weekly',
        priority: pageType === 'home' ? 1.0 : 0.8,
      });
      entries.push(entry);
    });
  });

  return entries;
}

/**
 * 获取本地化路径配置
 */
function getLocalizedPaths() {
  const localizedPaths = {};

  Object.entries(PATHS_CONFIG).forEach(([pageType, paths]) => {
    if (pageType !== 'home') {
      localizedPaths[paths.en] = paths;
    }
  });

  return localizedPaths;
}

module.exports = {
  SITE_CONFIG,
  LOCALES_CONFIG,
  PATHS_CONFIG,
  getLocalizedPath,
  generateCanonicalURL,
  generateHreflangLinks,
  generateSitemapEntry,
  generateAllSitemapEntries,
  getLocalizedPaths,
};
