// This file configures the initialization of Sentry for edge runtime
// The config you add here will be used whenever a page or API route is run in edge runtime
// https://docs.sentry.io/platforms/javascript/guides/nextjs/
import * as Sentry from '@sentry/nextjs';

// Sampling rate constants
const MIDDLEWARE_SAMPLE_RATE = 0.1;
const PRODUCTION_SAMPLE_RATE = 0.1;
const DEVELOPMENT_SAMPLE_RATE = 1.0;
const API_SAMPLE_RATE = 1.0;

// Environment variables with fallbacks
const SENTRY_DSN = process.env['SENTRY_DSN'] || '';
const NODE_ENV = process.env['NODE_ENV'] || 'development';
const VERCEL_GIT_COMMIT_SHA =
  process.env['VERCEL_GIT_COMMIT_SHA'] || 'development';

Sentry.init({
  dsn: SENTRY_DSN,

  // Adjust this value in production, or use tracesSampler for greater control
  tracesSampleRate: 1,

  // Setting this option to true will print useful information to the console while you're setting up Sentry.
  debug: false,

  // Set user context for edge runtime
  initialScope: {
    tags: {
      component: 'edge',
    },
  },

  // Environment configuration
  environment: NODE_ENV,

  // Release tracking
  release: VERCEL_GIT_COMMIT_SHA,

  // Edge runtime error filtering
  beforeSend(event) {
    // Filter out certain edge runtime errors
    if (process.env.NODE_ENV === 'development') {
      // Don't send certain errors in development
      if (event.exception) {
        const error = event.exception.values?.[0];
        if (error?.type === 'TypeError' && error?.value?.includes('fetch')) {
          return null;
        }
      }
    }
    return event;
  },

  // Custom edge tags
  beforeSendTransaction(event) {
    // Add custom tags to edge transactions
    event.tags = {
      ...event.tags,
      section: 'edge',
      runtime: 'edge',
    };
    return event;
  },

  // Configure sampling for edge runtime
  tracesSampler: (samplingContext) => {
    // Sample middleware at a lower rate
    if (samplingContext.name === 'middleware') {
      return MIDDLEWARE_SAMPLE_RATE;
    }

    // Sample API routes at a higher rate
    if (samplingContext['request']?.url?.includes('/api/')) {
      return API_SAMPLE_RATE;
    }

    // Default sampling
    return NODE_ENV === 'production'
      ? PRODUCTION_SAMPLE_RATE
      : DEVELOPMENT_SAMPLE_RATE;
  },
});
