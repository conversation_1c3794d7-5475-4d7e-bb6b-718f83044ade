# Git Hooks管理配置
# 轻量级Git工作流配置，支持快速迭代开发
pre-commit:
  parallel: false
  commands:
    # 轻量级质量检查 - 只检查暂存文件，快速反馈
    quick-quality:
      run: pnpm quality:quick:staged
      fail_text: '轻量级质量检查失败 - 请修复问题后重新提交'
      stage_fixed: true

commit-msg:
  commands:
    commitlint:
      run: pnpm commitlint --edit {1}
      fail_text:
        '提交信息不符合规范 - 请使用约定式提交格式：type(scope): description'

pre-push:
  commands:
    # 推送前运行完整的轻量级检查
    full-quality:
      run: pnpm quality:quick:verbose
      fail_text: '质量检查失败 - 请修复问题后再推送'
    test:
      run: pnpm test
      fail_text: '测试失败 - 请确保所有测试通过后再推送'
    # 暂时跳过构建检查，直到ESLint错误修复
    # build:
    #   run: pnpm build
    #   fail_text: '构建失败 - 请修复构建错误后再推送'
