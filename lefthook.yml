# Git Hooks管理配置
# 企业级Git工作流配置，确保代码质量和提交规范
pre-commit:
  parallel: true
  commands:
    # 快速检查组 - 并行执行
    type-check:
      run: pnpm type-check:strict
      fail_text: 'TypeScript类型检查失败 - 请修复类型错误后重新提交'
      stage_fixed: true
    lint:
      run: pnpm lint:strict
      fail_text: 'ESLint检查失败 - 请运行 pnpm lint:fix 修复问题'
      stage_fixed: true
    format:
      run: pnpm format:check
      fail_text: 'Prettier格式检查失败 - 请运行 pnpm format:write 格式化代码'
      stage_fixed: true
    arch-check:
      run: pnpm arch:validate
      fail_text: '架构一致性检查失败 - 请检查循环依赖和架构规则'
    security-check:
      run: pnpm security:check
      fail_text: '安全扫描检查失败 - 发现安全漏洞，请修复后提交'
    duplication-check:
      run: pnpm duplication:check
      fail_text: '代码重复度检查失败 - 重复度>3%，请重构重复代码'

    # 性能预算检查 - 使用--silent避免TTY冲突
    size-check:
      run: pnpm size-limit --silent
      fail_text: '性能预算检查失败 - 包大小超出限制，请优化后提交'

commit-msg:
  commands:
    commitlint:
      run: pnpm commitlint --edit {1}
      fail_text:
        '提交信息不符合规范 - 请使用约定式提交格式：type(scope): description'

pre-push:
  commands:
    test:
      run: pnpm test
      fail_text: '测试失败 - 请确保所有测试通过后再推送'
    build:
      run: pnpm build
      fail_text: '构建失败 - 请修复构建错误后再推送'
