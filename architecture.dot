strict digraph "dependency-cruiser output"{
    rankdir="TB" splines="ortho" overlap="false" nodesep="0.16" ranksep="0.18" fontname="Helvetica" fontsize="9" style="rounded,bold,filled" fillcolor="#ffffff" compound="true" bgcolor="transparent"
    node [shape="box" style="rounded, filled" height="0.2" color="black" fillcolor="#ffffcc" fontcolor="black" fontname="Helvetica" fontsize="9"]
    edge [arrowhead="normal" arrowsize="0.6" penwidth="2.0" color="#00000033" fontname="Helvetica" fontsize="9"]

    subgraph "cluster_src" {label="src" subgraph "cluster_src/app" {label="app" subgraph "cluster_src/app/error-test" {label="error-test" "src/app/error-test/page.tsx" [label=<page.tsx> tooltip="no-orphans" URL="src/app/error-test/page.tsx" fillcolor="#ffcccc" fontcolor="orange" color="orange" style="filled"] } } }
    subgraph "cluster_src" {label="src" subgraph "cluster_src/app" {label="app" "src/app/globals.css" [label=<globals.css> tooltip="globals.css" URL="src/app/globals.css" fillcolor="#ffcccc" style="filled"] } }
    subgraph "cluster_src" {label="src" subgraph "cluster_src/app" {label="app" "src/app/layout.tsx" [label=<layout.tsx> tooltip="layout.tsx" URL="src/app/layout.tsx" fillcolor="#ffcccc" style="filled"] } }
    "src/app/layout.tsx" -> "src/app/globals.css"
    subgraph "cluster_src" {label="src" subgraph "cluster_src/app" {label="app" "src/app/page.tsx" [label=<page.tsx> tooltip="no-orphans" URL="src/app/page.tsx" fillcolor="#ffcccc" fontcolor="orange" color="orange" style="filled"] } }
    subgraph "cluster_src" {label="src" subgraph "cluster_src/features" {label="features" subgraph "cluster_src/features/auth" {label="auth" "src/features/auth/index.ts" [label=<index.ts> tooltip="no-orphans" URL="src/features/auth/index.ts" fillcolor="#ffffcc" fontcolor="orange" color="orange" style="filled"] } } }
    subgraph "cluster_src" {label="src" subgraph "cluster_src/features" {label="features" subgraph "cluster_src/features/dashboard" {label="dashboard" "src/features/dashboard/index.ts" [label=<index.ts> tooltip="no-orphans" URL="src/features/dashboard/index.ts" fillcolor="#ffffcc" fontcolor="orange" color="orange" style="filled"] } } }
    subgraph "cluster_src" {label="src" subgraph "cluster_src/shared" {label="shared" "src/shared/utils.ts" [label=<utils.ts> tooltip="no-orphans" URL="src/shared/utils.ts" fillcolor="#ccffcc" fontcolor="orange" color="orange"] } }
}
