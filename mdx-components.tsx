import Image, { ImageProps } from 'next/image';

import type { MDXComponents } from 'mdx/types';

// This file allows you to provide custom React components
// to be used in MDX files. You can import and use any
// React component you want, including inline styles,
// components from other libraries, and more.

export function useMDXComponents(components: MDXComponents): MDXComponents {
  return {
    // Allows customizing built-in components, e.g. to add styling.
    h1: ({ children }) => (
      <h1 className='mb-6 text-4xl font-bold text-gray-900 dark:text-gray-100'>
        {children}
      </h1>
    ),
    h2: ({ children }) => (
      <h2 className='mb-4 text-3xl font-semibold text-gray-800 dark:text-gray-200'>
        {children}
      </h2>
    ),
    h3: ({ children }) => (
      <h3 className='mb-3 text-2xl font-medium text-gray-700 dark:text-gray-300'>
        {children}
      </h3>
    ),
    p: ({ children }) => (
      <p className='mb-4 leading-relaxed text-gray-600 dark:text-gray-400'>
        {children}
      </p>
    ),
    a: ({ children, href }) => (
      <a
        href={href}
        className='text-blue-600 underline hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300'
      >
        {children}
      </a>
    ),
    ul: ({ children }) => (
      <ul className='mb-4 list-inside list-disc space-y-2 text-gray-600 dark:text-gray-400'>
        {children}
      </ul>
    ),
    ol: ({ children }) => (
      <ol className='mb-4 list-inside list-decimal space-y-2 text-gray-600 dark:text-gray-400'>
        {children}
      </ol>
    ),
    li: ({ children }) => (
      <li className='text-gray-600 dark:text-gray-400'>{children}</li>
    ),
    blockquote: ({ children }) => (
      <blockquote className='mb-4 border-l-4 border-blue-500 pl-4 text-gray-700 italic dark:text-gray-300'>
        {children}
      </blockquote>
    ),
    code: ({ children }) => (
      <code className='rounded bg-gray-100 px-2 py-1 font-mono text-sm text-gray-800 dark:bg-gray-800 dark:text-gray-200'>
        {children}
      </code>
    ),
    pre: ({ children }) => (
      <pre className='mb-4 overflow-x-auto rounded-lg bg-gray-100 p-4 dark:bg-gray-800'>
        {children}
      </pre>
    ),
    img: (props) => {
      const { alt, ...restProps } = props as ImageProps;
      return (
        <Image
          alt={alt || ''}
          sizes='100vw'
          style={{ width: '100%', height: 'auto' }}
          className='rounded-lg shadow-md'
          {...restProps}
        />
      );
    },
    hr: () => <hr className='my-8 border-gray-300 dark:border-gray-700' />,
    table: ({ children }) => (
      <div className='mb-4 overflow-x-auto'>
        <table className='min-w-full border-collapse border border-gray-300 dark:border-gray-700'>
          {children}
        </table>
      </div>
    ),
    th: ({ children }) => (
      <th className='border border-gray-300 bg-gray-100 px-4 py-2 text-left font-semibold dark:border-gray-700 dark:bg-gray-800'>
        {children}
      </th>
    ),
    td: ({ children }) => (
      <td className='border border-gray-300 px-4 py-2 dark:border-gray-700'>
        {children}
      </td>
    ),
    ...components,
  };
}
