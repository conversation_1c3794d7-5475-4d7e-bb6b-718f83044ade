{"timestamp": "2025-08-02T07:18:23.826Z", "project": "tucsenberg-web-frontier", "summary": {"overallScore": 70, "totalChecks": 10, "passedChecks": 5, "failedChecks": 5}, "categories": {"codeQuality": {"score": 0, "details": [{"check": "TypeScript严格类型检查", "command": "pnpm type-check:strict", "status": "FAIL", "error": "Command failed: pnpm type-check:strict"}, {"check": "ESLint代码质量检查", "command": "pnpm lint:strict", "status": "FAIL", "error": "Command failed: pnpm lint:strict"}, {"check": "Prettier代码格式检查", "command": "pnpm format:check", "status": "FAIL", "error": "Command failed: pnpm format:check"}, {"check": "代码重复度检查", "command": "pnpm duplication:check", "status": "FAIL", "error": "Command failed: pnpm duplication:check"}]}, "security": {"score": 50, "details": [{"check": "安全漏洞扫描", "command": "pnpm security:check", "status": "FAIL", "error": "Command failed: pnpm security:check"}, {"check": "依赖安全审计", "command": "pnpm audit --audit-level moderate", "status": "PASS", "output": "No known vulnerabilities found"}]}, "performance": {"score": 100, "details": [{"check": "包大小检查", "command": "pnpm size:check", "status": "PASS", "output": "> tucsenberg-web-frontier@0.1.0 size:check /Users/<USER>/Warehouse/Focus/tucsenberg-web-frontier\n> size-limit\n\n  \n  Main App Bundle (First Load JS)\n  Size limit:   50 kB\n  Size:         889 B with all dependencies, minified and brotlied\n  Loading time: 18 ms on slow 3G\n  \n  Framework Bundle\n  Size limit:   130 kB\n  Size:         49.52 kB with all dependencies, minified and brotlied\n  Loading time: 968 ms   on slow 3G\n  \n  Main Bundle\n  Size limit:   40 kB\n  Size:         1.68 kB with all dependencies, minified and brotlied\n  Loading time: 33 ms   on slow 3G\n  \n  Locale Page Bundle\n  Size limit:   15 kB\n  Size:         7.02 kB with all dependencies, minified and brotlied\n  Loading time: 138 ms  on slow 3G\n  \n  Total CSS Bundle\n  Size limit:   50 kB\n  Size:         9.7 kB with all dependencies, minified and brotlied\n  Loading time: 190 ms on slow 3G\n  \n  Shared Chunks (excluding framework)\n  Size limit:   265 kB\n  Size:         261.99 kB with all dependencies, minified and brotlied\n  Loading time: 5.2 s     on slow 3G\n  \n  Polyfills Bundle\n  Size limit:   50 kB\n  Size:         35.16 kB with all dependencies, minified and brotlied\n  Loading time: 687 ms   on slow 3G\n  \n  Webpack Runtime\n  Size limit:   10 kB\n  Size:         1.75 kB with all dependencies, minified and brotlied\n  Loading time: 35 ms   on slow 3G"}, {"check": "性能审计", "command": "timeout 10s pnpm perf:audit || echo \"Performance audit completed\"", "status": "PASS", "output": "Performance audit completed"}]}, "architecture": {"score": 100, "details": [{"check": "架构一致性验证", "command": "pnpm arch:validate", "status": "PASS", "output": "> tucsenberg-web-frontier@0.1.0 arch:validate /Users/<USER>/Warehouse/Focus/tucsenberg-web-frontier\n> pnpm arch:check && pnpm circular:check\n\n\n> tucsenberg-web-frontier@0.1.0 arch:check /Users/<USER>/Warehouse/Focus/tucsenberg-web-frontier\n> dependency-cruiser src --config .dependency-cruiser.js\n\n\n  warn no-orphans: src/types/content.ts\n  warn no-orphans: src/services/url-generator-cjs.js\n  warn no-orphans: src/lib/utils.ts\n  warn no-orphans: src/lib/theme-analytics.ts\n  warn no-orphans: src/features/dashboard/index.ts\n  warn no-orphans: src/features/auth/index.ts\n  warn no-orphans: src/constants/i18n-constants.ts\n  warn no-orphans: src/config/paths.ts\n  warn no-orphans: src/components/theme-provider.tsx\n  warn no-orphans: src/components/loading-spinner.tsx\n  warn no-orphans: src/components/i18n/translation-fallback.tsx\n  warn no-orphans: src/app/page.tsx\n  warn no-orphans: src/app/layout.tsx\n  warn no-orphans: src/app/error-test/page.tsx\n\nx 14 dependency violations (0 errors, 14 warnings). 104 modules, 106 dependencies cruised.\n\n\n> tucsenberg-web-frontier@0.1.0 circular:check /Users/<USER>/Warehouse/Focus/tucsenberg-web-frontier\n> madge --circular --extensions ts,tsx src\n\nProcessed 72 files (808ms) (36 warnings)"}]}, "testing": {"score": 100, "details": [{"check": "单元测试执行", "command": "pnpm test", "status": "PASS", "output": "> tucsenberg-web-frontier@0.1.0 test /Users/<USER>/Warehouse/Focus/tucsenberg-web-frontier\n> jest --passWithNoTests"}]}}, "recommendations": ["建议改进代码质量：修复ESLint警告，优化TypeScript类型定义", "建议加强安全措施：更新依赖包，修复安全漏洞"]}