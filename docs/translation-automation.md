# 翻译自动化工具文档

## 概述

本项目实现了完整的翻译自动化工具链，包括翻译键扫描、同步、验证和质量检查功能。支持与Lingo.dev等AI翻译服务集成，提供企业级的翻译管理解决方案。

## 功能特性

### 🔍 翻译键自动扫描

- 使用AST解析扫描代码中的翻译键
- 支持多种翻译函数识别
- 检测缺失和未使用的翻译键
- 生成详细的使用情况报告

### 🔄 翻译同步和更新

- 自动同步翻译文件结构
- 智能创建缺失的翻译键
- 备份机制保护数据安全
- 增量更新支持

### ✅ 翻译完整性检查

- 多维度质量验证
- 占位符一致性检查
- 翻译长度比例分析
- 可疑翻译检测

### 🤖 AI翻译质量验证

- 与Lingo.dev集成
- AI翻译结果质量评分
- 与人工翻译对比分析
- 质量基准对比

### 📊 质量报告和监控

- 详细的质量报告生成
- 翻译覆盖率统计
- 质量趋势分析
- CI/CD集成支持

## 工具列表

### 核心脚本

| 脚本                       | 命令                                  | 功能描述                   |
| -------------------------- | ------------------------------------- | -------------------------- |
| `translation-scanner.js`   | `pnpm scan:translations`              | 扫描代码中的翻译键使用情况 |
| `translation-sync.js`      | `pnpm sync:translations:enhanced`     | 同步和更新翻译文件         |
| `translation-validator.js` | `pnpm validate:translations:enhanced` | 验证翻译完整性和质量       |

### 组合命令

| 命令                 | 功能描述                               |
| -------------------- | -------------------------------------- |
| `pnpm i18n:full`     | 执行完整的翻译工作流（扫描→同步→验证） |
| `pnpm i18n:scan`     | 仅执行翻译键扫描                       |
| `pnpm i18n:sync`     | 仅执行翻译同步                         |
| `pnpm i18n:validate` | 仅执行翻译验证                         |

## 使用指南

### 基础使用

1. **扫描翻译键**

   ```bash
   pnpm scan:translations
   ```

   - 扫描所有源代码文件
   - 识别使用的翻译键
   - 检测缺失和未使用的键
   - 生成扫描报告

2. **同步翻译文件**

   ```bash
   pnpm sync:translations:enhanced
   ```

   - 自动创建备份
   - 同步翻译文件结构
   - 创建缺失的翻译键
   - 验证同步结果

3. **验证翻译质量**

   ```bash
   pnpm validate:translations:enhanced
   ```

   - 检查翻译完整性
   - 验证占位符一致性
   - 分析翻译质量
   - 生成质量报告

4. **完整工作流**
   ```bash
   pnpm i18n:full
   ```
   - 依次执行扫描、同步、验证
   - 生成综合报告
   - 适合CI/CD集成

### 高级配置

#### 配置文件

项目根目录的 `translation.config.js` 包含所有配置选项：

```javascript
module.exports = {
  locales: ['en', 'zh'],
  defaultLocale: 'en',
  messagesDir: './messages',

  // 扫描配置
  scanner: {
    patterns: ['src/**/*.{ts,tsx,js,jsx}'],
    translationFunctions: ['t', 'useTranslations'],
  },

  // 质量阈值
  validation: {
    thresholds: {
      minTranslationCoverage: 95,
      maxLengthRatio: 3.0,
    },
  },

  // Lingo.dev集成
  lingo: {
    enabled: false,
    apiKey: process.env.LINGO_API_KEY,
  },
};
```

#### 环境变量

```bash
# Lingo.dev集成
LINGO_API_KEY=your_api_key
LINGO_PROJECT_ID=your_project_id

# 通知配置
SLACK_WEBHOOK_URL=your_slack_webhook
```

## 报告说明

### 扫描报告 (`translation-scan-report.json`)

```json
{
  "summary": {
    "totalFiles": 51,
    "scannedFiles": 51,
    "uniqueKeys": 34,
    "missingKeys": ["key1", "key2"],
    "unusedKeys": ["key3", "key4"]
  },
  "keyUsages": {
    "home.title": [{ "file": "src/app/page.tsx", "line": 15 }]
  }
}
```

### 验证报告 (`translation-validation-report.json`)

```json
{
  "summary": {
    "totalKeys": 124,
    "issueCount": 5,
    "errorCount": 0,
    "warningCount": 5
  },
  "statistics": {
    "coverage": {
      "en": { "percentage": "100.00", "translated": 124 },
      "zh": { "percentage": "98.39", "translated": 122 }
    }
  },
  "issues": [
    {
      "type": "suspicious_translation",
      "severity": "warning",
      "key": "home.title",
      "message": "可能未翻译"
    }
  ]
}
```

## CI/CD集成

### GitHub Actions

项目包含完整的GitHub
Actions工作流 (`.github/workflows/translation-quality.yml`)：

- 自动运行翻译质量检查
- 在PR中显示翻译状态
- 上传报告作为构建产物
- 质量门禁控制

### 质量门禁

- ❌ 关键错误：构建失败
- ⚠️ 警告：构建通过但显示警告
- ✅ 无问题：构建通过

## Lingo.dev集成

### 配置步骤

1. 获取Lingo.dev API密钥
2. 设置环境变量
3. 启用集成功能
4. 配置质量阈值

### 功能特性

- AI翻译质量评分
- 与人工翻译对比
- 术语一致性检查
- 质量基准对比

## 最佳实践

### 开发工作流

1. **开发阶段**：使用 `pnpm scan:translations` 检查新增的翻译键
2. **提交前**：运行 `pnpm i18n:full` 确保翻译质量
3. **CI/CD**：自动运行翻译检查，确保质量门禁

### 翻译管理

1. **键命名**：使用层次化命名（如 `home.title`）
2. **占位符**：保持占位符一致性
3. **术语**：维护术语词典
4. **质量**：定期审查翻译质量报告

### 故障排除

1. **扫描失败**：检查代码语法错误
2. **同步问题**：检查翻译文件格式
3. **验证失败**：查看详细错误信息
4. **集成问题**：验证API密钥配置

## 扩展开发

### 添加新的验证规则

```javascript
// 在 translation-validator.js 中添加
function validateCustomRule(translations, allKeys) {
  const issues = [];
  // 自定义验证逻辑
  return issues;
}
```

### 集成新的翻译服务

```javascript
// 实现 AITranslationService 接口
class CustomTranslationService {
  async translate(text, fromLocale, toLocale) {
    // 翻译实现
  }
}
```

### 自定义报告格式

```javascript
// 在配置文件中指定模板
reporting: {
  formats: ['json', 'html', 'csv'],
  templates: {
    custom: './templates/custom-report.hbs'
  }
}
```

## 性能优化

- **缓存**：启用翻译结果缓存
- **并发**：配置合适的并发数
- **增量**：使用增量扫描和同步
- **过滤**：排除不必要的文件

## 支持和维护

- 查看 `reports/` 目录下的详细报告
- 检查 `logs/` 目录下的调试日志
- 参考 `translation.config.js` 配置选项
- 使用 `--help` 参数查看脚本帮助信息
