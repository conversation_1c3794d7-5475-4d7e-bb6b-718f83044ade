# 🛠️ 代码质量工具详细配置指南

## 📋 概述

本项目采用企业级代码质量保障体系，包含9个核心质量工具，确保代码质量、安全性、性能和可维护性达到最高标准。

## 🔧 核心质量工具配置

### 1. ESLint 9 - 代码规范和质量检查

**版本**: `eslint@9.29.0` **配置文件**: `eslint.config.mjs` (Flat Config)

#### 核心插件生态 (9个插件)

```javascript
// 基础插件
- @eslint/js                                    // JavaScript基础规则
- @next/eslint-plugin-next                      // Next.js专用规则
- @typescript-eslint/eslint-plugin              // TypeScript规则

// React生态插件
- eslint-plugin-react                           // React规则
- eslint-plugin-react-hooks                     // React Hooks规则
- eslint-plugin-react-you-might-not-need-an-effect // 🔴 useEffect优化

// 安全插件
- eslint-plugin-security                        // 通用安全规则
- eslint-plugin-security-node                   // Node.js安全规则

// 代码质量插件
- eslint-plugin-import                          // 导入/导出规则
- eslint-plugin-promise                         // Promise规则
```

#### 🔴 React You Might Not Need An Effect 配置

```javascript
{
  name: 'react-you-might-not-need-an-effect-config',
  files: ['**/*.{js,jsx,ts,tsx}'],
  plugins: {
    'react-you-might-not-need-an-effect': reactYouMightNotNeedAnEffect,
  },
  rules: {
    // 🔴 Enabled as error - 检测不必要的useEffect模式 (9个规则)
    'react-you-might-not-need-an-effect/no-empty-effect': 'error',
    'react-you-might-not-need-an-effect/no-reset-all-state-when-a-prop-changes': 'error',
    'react-you-might-not-need-an-effect/no-event-handler': 'error',
    'react-you-might-not-need-an-effect/no-pass-live-state-to-parent': 'error',
    'react-you-might-not-need-an-effect/no-pass-data-to-parent': 'error',
    'react-you-might-not-need-an-effect/no-manage-parent': 'error',
    'react-you-might-not-need-an-effect/no-initialize-state': 'error',
    'react-you-might-not-need-an-effect/no-chain-state-updates': 'error',
    'react-you-might-not-need-an-effect/no-derived-state': 'error',
  },
}
```

**检测场景 (9个反模式)**:

- **no-empty-effect**: 空的useEffect
- **no-reset-all-state-when-a-prop-changes**: 当prop变化时重置所有状态
- **no-event-handler**: 在useEffect中处理事件
- **no-pass-live-state-to-parent**: 向父组件传递实时状态
- **no-pass-data-to-parent**: 向父组件传递数据
- **no-manage-parent**: 在子组件中管理父组件状态
- **no-initialize-state**: 在useEffect中初始化状态
- **no-chain-state-updates**: 链式状态更新
- **no-derived-state**: 派生状态应该用计算属性

#### 企业级复杂度标准 (最严格)

```javascript
// 复杂度控制 (比标准更严格)
'complexity': ['error', 10],                    // 圈复杂度 ≤ 10
'max-depth': ['error', 4],                      // 嵌套深度 ≤ 4层
'max-lines-per-function': ['error', 80],        // 函数长度 ≤ 80行
'max-params': ['error', 5],                     // 参数数量 ≤ 5个
'max-nested-callbacks': ['error', 3],           // 回调嵌套 ≤ 3层
'max-lines': ['error', 500],                    // 文件长度 ≤ 500行
'max-statements': ['error', 30],                // 函数语句 ≤ 30个
```

#### 安全规则配置 (26个ESLint安全规则) - 🔴 全部Error级别

```javascript
// eslint-plugin-security (12个规则)
'security/detect-object-injection': 'error',
'security/detect-non-literal-regexp': 'error',
'security/detect-unsafe-regex': 'error',
'security/detect-buffer-noassert': 'error',
'security/detect-child-process': 'error',
'security/detect-disable-mustache-escape': 'error',
'security/detect-eval-with-expression': 'error',
'security/detect-no-csrf-before-method-override': 'error',
'security/detect-non-literal-fs-filename': 'error',
'security/detect-non-literal-require': 'error',
'security/detect-possible-timing-attacks': 'error',
'security/detect-pseudoRandomBytes': 'error',

// eslint-plugin-security-node (14个规则) - 全部升级为error
'security-node/detect-sql-injection': 'error',
'security-node/detect-nosql-injection': 'error',
'security-node/detect-html-injection': 'error',
'security-node/non-literal-reg-expr': 'error',
'security-node/detect-insecure-randomness': 'error',        // 🔴 升级为error
'security-node/detect-dangerous-redirects': 'error',
'security-node/detect-eval-with-expr': 'error',
'security-node/detect-improper-exception-handling': 'error', // 🔴 升级为error
'security-node/detect-non-literal-require-calls': 'error',
'security-node/detect-possible-timing-attacks': 'error',     // 🔴 升级为error
'security-node/detect-unhandled-async-errors': 'error',
'security-node/detect-unhandled-event-errors': 'error',
'security-node/detect-security-missconfiguration-cookie': 'error',
'security-node/disable-ssl-across-node-server': 'error',
```

### 2. Prettier - 代码格式化

**版本**: `prettier@3.5.3` **配置文件**: `.prettierrc.json`

#### 核心配置

```json
{
  "semi": true, // 强制分号
  "trailingComma": "all", // 尾随逗号
  "singleQuote": true, // 单引号
  "printWidth": 80, // 行宽80字符
  "tabWidth": 2, // 缩进2空格
  "useTabs": false, // 使用空格而非Tab
  "jsxSingleQuote": true, // JSX单引号
  "bracketSameLine": false, // 括号换行
  "arrowParens": "always", // 箭头函数括号
  "endOfLine": "lf", // Unix换行符
  "singleAttributePerLine": true // JSX属性单行
}
```

#### 插件生态

```json
"plugins": [
  "@trivago/prettier-plugin-sort-imports",     // 导入排序
  "prettier-plugin-tailwindcss"                // Tailwind类名排序
]
```

#### 导入排序规则

```json
"importOrder": [
  "^react$",                                   // React核心
  "^react/(.*)$",                             // React相关
  "^next$",                                   // Next.js核心
  "^next/(.*)$",                              // Next.js相关
  "<THIRD_PARTY_MODULES>",                    // 第三方库
  "^@/types/(.*)$",                           // 类型定义
  "^@/lib/(.*)$",                             // 工具库
  "^@/components/(.*)$",                      // 组件
  "^@/app/(.*)$",                             // 应用代码
  "^@/(.*)$",                                 // 其他内部模块
  "^[./]"                                     // 相对导入
]
```

### 3. TypeScript - 类型检查

**版本**: `typescript@5.8.3` **配置文件**: `tsconfig.json`

#### 最严格类型检查配置

```json
{
  "compilerOptions": {
    // 最严格的类型检查
    "strict": true,
    "noImplicitAny": true,
    "noImplicitReturns": true,
    "noImplicitThis": true,
    "noImplicitOverride": true,
    "strictNullChecks": true,
    "strictFunctionTypes": true,
    "strictBindCallApply": true,
    "strictPropertyInitialization": true,
    "alwaysStrict": true,

    // 最严格的代码质量检查
    "noUnusedLocals": true,
    "noUnusedParameters": true,
    "exactOptionalPropertyTypes": true,
    "noUncheckedIndexedAccess": true,
    "noPropertyAccessFromIndexSignature": true,
    "allowUnusedLabels": false,
    "allowUnreachableCode": false
  }
}
```

### 4. Jest - 测试框架

**版本**: `jest@29.7.0` **配置文件**: `jest.config.js`

#### 测试覆盖率要求

```javascript
coverageThreshold: {
  global: {
    branches: 80,                               // 分支覆盖率 ≥ 80%
    functions: 80,                              // 函数覆盖率 ≥ 80%
    lines: 80,                                  // 行覆盖率 ≥ 80%
    statements: 80,                             // 语句覆盖率 ≥ 80%
  },
}
```

### 5. dependency-cruiser - 架构一致性检查

**版本**: `dependency-cruiser@16.8.0` **配置文件**: `.dependency-cruiser.js`

#### 核心架构规则

```javascript
forbidden: [
  {
    name: 'no-circular', // 禁止循环依赖
    severity: 'error',
    from: {},
    to: { circular: true },
  },
  {
    name: 'feature-isolation', // 特性间依赖隔离
    severity: 'error',
    from: { path: '^src/features/[^/]+' },
    to: { path: '^src/features/(?!\\1)[^/]+' },
  },
  {
    name: 'no-external-to-internal', // 禁止外部访问内部模块
    severity: 'error',
    from: { pathNot: '^src/' },
    to: { path: '^src/lib/internal' },
  },
];
```

### 6. madge - 循环依赖检测

**版本**: `madge@8.0.0` **用途**: 辅助dependency-cruiser进行循环依赖分析

#### 使用命令

```bash
pnpm circular:check                             # 检测循环依赖
pnpm circular:report                            # 生成JSON报告
pnpm circular:image                             # 生成可视化图表
```

### 7. jscpd - 代码重复度检测

**版本**: `jscpd@4.0.5` **配置文件**: `.jscpd.json`

#### 重复度检测配置

```json
{
  "threshold": 3, // 重复度阈值 3%
  "reporters": ["html", "console", "badge"], // 报告格式
  "minLines": 5, // 最小检测行数
  "minTokens": 50, // 最小token数量
  "maxLines": 500, // 最大文件行数
  "maxSize": "30kb", // 最大文件大小
  "formatsExts": {
    "typescript": ["ts", "tsx"],
    "javascript": ["js", "jsx"]
  },
  "output": "./reports/jscpd", // 报告输出目录
  "exitCode": 1 // 超标时退出码
}
```

#### 忽略规则

```json
"ignore": [
  "**/*.test.ts",                               // 测试文件
  "**/*.test.tsx",
  "**/*.spec.ts",
  "**/*.spec.tsx",
  "**/node_modules/**",                         // 依赖目录
  "**/.next/**",                                // 构建目录
  "**/build/**",
  "**/dist/**",
  "**/*.d.ts",                                  // 类型定义
  "**/coverage/**"                              // 覆盖率报告
]
```

### 8. size-limit - 性能预算控制

**版本**: `size-limit@11.2.0` **配置文件**: `.size-limit.js`

#### Bundle大小限制 (8个Bundle)

```javascript
module.exports = [
  {
    name: 'Main App Bundle (First Load JS)',
    path: '.next/static/chunks/main-app-*.js',
    limit: '50 KB', // 主应用包 ≤ 50KB
  },
  {
    name: 'Framework Bundle',
    path: '.next/static/chunks/framework-*.js',
    limit: '130 KB', // 框架包 ≤ 130KB
  },
  {
    name: 'Main Bundle',
    path: '.next/static/chunks/main-*.js',
    limit: '40 KB', // 主包 ≤ 40KB
  },
  {
    name: 'Locale Page Bundle',
    path: '.next/static/chunks/app/\\[locale\\]/page-*.js',
    limit: '15 KB', // 页面包 ≤ 15KB
  },
  {
    name: 'Total CSS Bundle',
    path: '.next/static/css/*.css',
    limit: '50 KB', // CSS包 ≤ 50KB
  },
  {
    name: 'Shared Chunks',
    path: '.next/static/chunks/!(framework|main|main-app|polyfills|webpack)-*.js',
    limit: '260 KB', // 共享包 ≤ 260KB
  },
  {
    name: 'Polyfills Bundle',
    path: '.next/static/chunks/polyfills-*.js',
    limit: '50 KB', // Polyfills ≤ 50KB
  },
  {
    name: 'Webpack Runtime',
    path: '.next/static/chunks/webpack-*.js',
    limit: '10 KB', // Webpack运行时 ≤ 10KB
  },
];
```

### 9. Semgrep - 静态安全分析

**版本**: `semgrep@1.130.0` **配置文件**: `semgrep.yml`

#### 自定义安全规则 (10个规则)

```yaml
rules:
  # Next.js XSS防护
  - id: nextjs-unsafe-dangerouslySetInnerHTML
    pattern-regex: "dangerouslySetInnerHTML=\\{\\{__html:\\s*\\$[A-Za-z_][A-Za-z0-9_]*\\}\\}"
    message: '避免使用dangerouslySetInnerHTML，存在XSS风险'
    languages: [typescript, javascript]
    severity: ERROR

  # 硬编码密钥检测
  - id: hardcoded-api-keys
    pattern-regex: "(api[_-]?key|secret[_-]?key|access[_-]?token)\\s*[=:]\\s*['\"][a-zA-Z0-9]{20,}['\"]"
    message: '检测到硬编码的API密钥或访问令牌'
    languages: [typescript, javascript]
    severity: ERROR

  # 不安全的eval使用
  - id: unsafe-eval-usage
    pattern-either:
      - pattern: eval($ARG)
      - pattern: Function($ARG)
      - pattern: new Function($ARG)
    message: '避免使用eval()或Function构造器，存在代码注入风险'
    languages: [typescript, javascript]
    severity: ERROR

  # SQL注入检测
  - id: sql-injection-risk
    pattern: $DB.query($QUERY)
    message: '可能的SQL注入风险，请使用参数化查询'
    languages: [typescript, javascript]
    severity: ERROR

  # 不安全的随机数生成
  - id: insecure-random
    pattern-either:
      - pattern: Math.random()
      - pattern: new Date().getTime()
    message: '不安全的随机数生成，请使用crypto.randomBytes()'
    languages: [typescript, javascript]
    severity: WARNING
```

#### 忽略配置 (.semgrepignore)

```
node_modules/
.next/
build/
dist/
*.test.ts
*.test.tsx
*.spec.ts
*.spec.tsx
```

## 🔄 质量检查流程

### 自动化检查命令

```bash
# 完整质量检查 (9个工具)
pnpm quality:full

# 基础质量检查 (5个工具)
pnpm quality:check:strict

# 单独工具检查
pnpm type-check:strict                          # TypeScript严格检查
pnpm lint:strict                                # ESLint严格检查
pnpm format:check                               # Prettier格式检查
pnpm arch:validate                              # 架构一致性检查
pnpm security:check                             # 安全扫描检查
pnpm duplication:check                          # 代码重复度检查
pnpm size:check                                 # 性能预算检查
pnpm test                                       # 测试执行
pnpm build                                      # 构建验证
```

### Git Hooks集成 (lefthook.yml)

```yaml
pre-commit:
  parallel: true
  commands:
    type-check:
      run: pnpm type-check:strict
    lint:
      run: pnpm lint:strict
    format:
      run: pnpm format:check
    arch-check:
      run: pnpm arch:validate
    security-check:
      run: pnpm security:check
    duplication-check:
      run: pnpm duplication:check
    size-check:
      run: pnpm size-limit --silent
```

## 🎯 ESLint规则级别策略

### 规则级别分布 (最新更新)

#### 🔴 Error级别 (103个规则) - 零容忍策略

- **安全规则**: 26个 (security + security-node)
- **React规则**: 9个 (react-you-might-not-need-an-effect)
- **代码质量**: 68个 (复杂度、最佳实践、代码风格)

#### 🟡 Warning级别 (0个规则) - 已全部升级

- **之前的Warning规则已全部升级为Error**:
  - `security-node/detect-insecure-randomness`: warn → error
  - `security-node/detect-improper-exception-handling`: warn → error
  - `security-node/detect-possible-timing-attacks`: warn → error
  - `default-case`: warn → error
  - `no-magic-numbers`: warn → error

#### ⚪ Off级别 (1个规则) - 明确禁用

- `no-ternary`: 'off' - 允许三元运算符的合理使用

### 升级理由

#### 安全规则升级 (3个)

```javascript
// 🔴 不安全的随机数生成 - 升级理由: 安全风险
'security-node/detect-insecure-randomness': 'error',
// Math.random()在安全场景中不可接受，必须使用crypto.randomBytes()

// 🔴 不当异常处理 - 升级理由: 信息泄露风险
'security-node/detect-improper-exception-handling': 'error',
// 异常信息可能泄露敏感数据，必须正确处理

// 🔴 可能的时序攻击 - 升级理由: 安全漏洞
'security-node/detect-possible-timing-attacks': 'error',
// 时序攻击可能导致密码破解，必须使用安全比较
```

#### 代码风格升级 (2个)

```javascript
// 🔴 switch语句必须有default case - 升级理由: 防御性编程
'default-case': 'error',
// 确保所有可能的情况都被考虑，提高代码健壮性

// 🔴 禁止魔法数字 - 升级理由: 代码可读性和维护性
'no-magic-numbers': ['error', { ignore: [0, 1, -1], ignoreArrayIndexes: true }],
// 魔法数字降低代码可读性，必须定义为有意义的常量
```

## 📊 质量标准

### 企业级质量阈值

- **代码复杂度**: ≤ 10 (圈复杂度)
- **函数长度**: ≤ 80行
- **文件长度**: ≤ 500行
- **嵌套深度**: ≤ 4层
- **参数数量**: ≤ 5个
- **代码重复度**: < 3%
- **测试覆盖率**: ≥ 80%
- **安全漏洞**: 0个高危，0个中危
- **Bundle大小**: 严格控制在预算范围内
- **架构违规**: 0个错误

### 质量门禁

- **通过率要求**: 100%
- **执行模式**: sequential (顺序执行)
- **失败策略**: failFast (快速失败)
- **估算时间**: 90-120秒

## 🎯 最佳实践

1. **渐进式质量提升**: 从基础4个工具逐步增加到完整9个工具
2. **配置模板化**: 使用`enhanced-quality-template.json`统一配置
3. **任务特定调整**: 根据任务需求添加专门的测试工具
4. **持续监控**: 通过Git hooks确保每次提交都通过质量检查
5. **团队协作**: 统一的代码风格和质量标准，提升协作效率
