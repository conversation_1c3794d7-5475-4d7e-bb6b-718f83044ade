{"common": {"loading": "Loading...", "error": "An error occurred", "success": "Success", "cancel": "Cancel", "confirm": "Confirm", "save": "Save", "edit": "Edit", "delete": "Delete", "search": "Search", "filter": "Filter", "sort": "Sort", "next": "Next", "previous": "Previous", "close": "Close", "open": "Open"}, "navigation": {"home": "Home", "about": "About", "contact": "Contact", "services": "Services", "products": "Products", "blog": "Blog", "login": "<PERSON><PERSON>", "logout": "Logout", "profile": "Profile", "settings": "Settings"}, "theme": {"toggle": "Toggle theme", "light": "Light", "dark": "Dark", "system": "System"}, "language": {"toggle": "Toggle language", "selectLanguage": "Select Language", "english": "English", "chinese": "中文", "switching": "Switching language...", "switchSuccess": "Language switched successfully", "switchError": "Failed to switch language", "fallbackWarning": "Some content may not be available in your selected language", "detectionInfo": "Detection Info", "source": "Source", "confidence": "Confidence", "userPreference": "User preference saved"}, "home": {"title": "Tucsenberg Web Frontier", "subtitle": "Modern B2B Enterprise Web Platform", "description": "A cutting-edge web platform built with Next.js 15, React 19, and TypeScript for modern B2B enterprises.", "getStarted": "Get Started", "learnMore": "Learn More", "features": {"title": "Features", "performance": {"title": "High Performance", "description": "Optimized for speed and efficiency with modern web technologies."}, "scalable": {"title": "Scalable Architecture", "description": "Built to grow with your business needs and requirements."}, "secure": {"title": "Enterprise Security", "description": "Industry-standard security practices and compliance."}}}, "themeDemo": {"title": "Theme System Demo", "description": "Testing theme switching and Chinese-English font mixing", "chineseTest": "Chinese Font Test", "englishTest": "English Font Test", "chineseDescription": "This is Chinese font testing content, using PingFang SC font fallback strategy. Modern B2B enterprise design style showcase.", "englishDescription": "This is English font testing content using Geist Sans font. Modern B2B enterprise design style showcase.", "chineseInput": "Chinese Input", "englishInput": "English Input", "chinesePlaceholder": "Enter Chinese content", "englishPlaceholder": "Enter English content", "primaryButton": "Primary", "secondaryButton": "Secondary", "outlineButton": "Outline"}, "instructions": {"getStarted": "Get started by editing", "saveChanges": "Save and see your changes instantly."}, "actions": {"deployNow": "Deploy now", "readDocs": "Read our docs"}, "footer": {"learn": "Learn", "examples": "Examples", "goToNextjs": "Go to nextjs.org"}, "formatting": {"date": {"today": "Today", "yesterday": "Yesterday", "tomorrow": "Tomorrow", "lastUpdated": "Last updated on {date}", "publishedOn": "Published on {date}"}, "number": {"currency": "${amount}", "percentage": "{value}%", "fileSize": "{size} {unit}"}, "plurals": {"items": {"zero": "No items", "one": "One item", "other": "{count} items"}, "users": {"zero": "No users online", "one": "One user online", "other": "{count} users online"}, "notifications": {"zero": "No new notifications", "one": "One new notification", "other": "{count} new notifications"}}}, "errors": {"notFound": "Page not found", "serverError": "Internal server error", "networkError": "Network connection error", "validationError": "Please check your input", "unauthorized": "You are not authorized to access this page", "forbidden": "Access to this resource is forbidden", "timeout": "Request timeout - please try again", "generic": "Something went wrong. Please try again later.", "translationMissing": "Translation not available", "loadingFailed": "Failed to load content"}, "accessibility": {"skipToContent": "Skip to main content", "openMenu": "Open navigation menu", "closeMenu": "Close navigation menu", "loading": "Content is loading", "error": "An error has occurred", "languageSelector": "Select language", "themeSelector": "Select theme"}, "seo": {"title": "Tucsenberg Web Frontier", "description": "Modern B2B Enterprise Web Platform with Next.js 15, React 19, and TypeScript", "siteName": "Tucsenberg Web Frontier", "keywords": "Next.js, React, TypeScript, B2B, Enterprise, Web Platform"}, "structured-data": {"organization": {"name": "Tucsenberg Web Frontier", "description": "Modern B2B Enterprise Web Platform", "phone": "******-0123", "social": {"twitter": "https://twitter.com/tucsenberg", "linkedin": "https://linkedin.com/company/tucsenberg", "github": "https://github.com/tucsenberg"}}, "website": {"name": "Tucsenberg Web Frontier", "description": "Modern B2B Enterprise Web Platform with Next.js 15"}, "article": {"defaultTitle": "Article", "defaultDescription": "Article description", "defaultAuthor": "Tucsenberg Team"}, "product": {"defaultName": "Product", "defaultDescription": "Product description"}}}