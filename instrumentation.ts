export async function register() {
  try {
    if (process.env['NEXT_RUNTIME'] === 'nodejs') {
      await import('./sentry.server.config');
    }

    if (process.env['NEXT_RUNTIME'] === 'edge') {
      await import('./sentry.edge.config');
    }
  } catch (error) {
    // 记录错误但不阻止应用启动
    // 在生产环境中，这里可以使用专门的日志服务
    if (process.env.NODE_ENV === 'development') {
      // eslint-disable-next-line no-console
      console.error('Failed to register instrumentation:', error);
    }
  }
}
