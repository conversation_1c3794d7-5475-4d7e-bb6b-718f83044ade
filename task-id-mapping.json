{"a1b2c3d4-e5f6-7890-abcd-ef1234567890": {"newId": "1ea07a45-4606-4217-bb3f-7cd5d26272cf", "taskName": "P0级架构一致性检查配置（dependency-cruiser + madge）"}, "a1b2c3d4-e5f6-7890-abcd-ef1234567891": {"newId": "03e8d12a-7bce-4cd8-8a2f-a0b2e97c84f4", "taskName": "P0级安全扫描强化配置（eslint-plugin-security-node + semgrep）"}, "a1b2c3d4-e5f6-7890-abcd-ef1234567892": {"newId": "78fe619b-179a-44d1-af4d-a1787178f163", "taskName": "P0级性能预算控制配置（size-limit + bundle分析）"}, "a1b2c3d4-e5f6-7890-abcd-ef1234567893": {"newId": "8f8754b6-c724-4022-b630-847f68a0c791", "taskName": "P0级代码重复度检测配置（jscpd + 重复度分析）"}, "sentry-monitoring-core-2025-0728": {"newId": "c0fa19a7-8bc1-48a6-881f-3989314eb4bc", "taskName": "基础错误监控与可观察性配置（Sentry核心监控）"}, "gh-actions-001-2025-0728": {"newId": "091966ed-0fce-47c3-ac77-96821f45b6fb", "taskName": "GitHub Actions基础CI/CD配置"}, "renovate-001-2025-0728": {"newId": "90f73e79-f3cf-49c1-a9e3-2af0ddfe72f8", "taskName": "Renovate依赖管理配置"}, "a2b3c4d5-e6f7-8901-bcde-f23456789012": {"newId": "54c01c15-c217-41a7-b898-9059f28729c4", "taskName": "SEO优化配置（next-sitemap + 结构化数据 + metadata工具函数）"}}