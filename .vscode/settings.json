{"editor.formatOnSave": true, "editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit", "source.organizeImports": "explicit"}, "eslint.workingDirectories": ["."], "typescript.preferences.importModuleSpecifier": "relative", "emmet.includeLanguages": {"typescript": "html", "typescriptreact": "html"}, "files.associations": {"*.css": "tailwindcss"}, "tailwindCSS.includeLanguages": {"typescript": "html", "typescriptreact": "html"}, "editor.rulers": [80, 100], "editor.tabSize": 2, "editor.insertSpaces": true, "files.trimTrailingWhitespace": true, "files.insertFinalNewline": true, "typescript.preferences.includePackageJsonAutoImports": "on", "typescript.suggest.autoImports": true, "typescript.updateImportsOnFileMove.enabled": "always", "eslint.validate": ["javascript", "javascriptreact", "typescript", "typescriptreact"], "prettier.requireConfig": true, "search.exclude": {"**/node_modules": true, "**/dist": true, "**/.next": true, "**/coverage": true, "**/reports": true}, "i18n-ally.localesPaths": ["messages", "src/i18n", "src/components/i18n"]}