{"semi": true, "trailingComma": "all", "singleQuote": true, "printWidth": 80, "tabWidth": 2, "useTabs": false, "quoteProps": "consistent", "jsxSingleQuote": true, "bracketSpacing": true, "bracketSameLine": false, "arrowParens": "always", "endOfLine": "lf", "embeddedLanguageFormatting": "auto", "htmlWhitespaceSensitivity": "strict", "insertPragma": false, "proseWrap": "always", "requirePragma": false, "singleAttributePerLine": true, "plugins": ["@trivago/prettier-plugin-sort-imports", "prettier-plugin-tailwindcss"], "importOrder": ["^react$", "^react/(.*)$", "^next$", "^next/(.*)$", "<THIRD_PARTY_MODULES>", "^@/types/(.*)$", "^@/lib/(.*)$", "^@/components/(.*)$", "^@/app/(.*)$", "^@/(.*)$", "^[./]"], "importOrderSeparation": true, "importOrderSortSpecifiers": true, "importOrderParserPlugins": ["typescript", "jsx", "decorators-legacy"], "importOrderCaseInsensitive": true, "tailwindConfig": "./tailwind.config.js", "tailwindFunctions": ["clsx", "cn", "cva", "tw"]}