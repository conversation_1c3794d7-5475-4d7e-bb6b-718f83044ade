rules:
  - id: nextjs-unsafe-dangerouslySetInnerHTML
    pattern-regex: "dangerouslySetInnerHTML=\\{\\{__html:\\s*\\$[A-Za-z_][A-Za-z0-9_]*\\}\\}"
    message: '避免使用dangerouslySetInnerHTML，存在XSS风险'
    languages: [typescript, javascript]
    severity: ERROR
    metadata:
      category: security
      technology: [nextjs, react]
      cwe: 'CWE-79: Cross-site Scripting (XSS)'
      owasp: 'A03:2021 - Injection'

  - id: hardcoded-api-keys
    pattern-regex: "(api[_-]?key|secret[_-]?key|access[_-]?token)\\s*[=:]\\s*['\"][a-zA-Z0-9]{20,}['\"]"
    message: '检测到硬编码的API密钥或访问令牌'
    languages: [typescript, javascript]
    severity: ERROR
    metadata:
      category: security
      technology: [nodejs]
      cwe: 'CWE-798: Use of Hard-coded Credentials'
      owasp: 'A07:2021 - Identification and Authentication Failures'

  - id: unsafe-eval-usage
    pattern-either:
      - pattern: eval($ARG)
      - pattern: Function($ARG)
      - pattern: new Function($ARG)
    message: '避免使用eval()或Function构造器，存在代码注入风险'
    languages: [typescript, javascript]
    severity: ERROR
    metadata:
      category: security
      technology: [nodejs]
      cwe:
        'CWE-95: Improper Neutralization of Directives in Dynamically Evaluated
        Code'
      owasp: 'A03:2021 - Injection'

  - id: nextjs-unsafe-redirect
    pattern-either:
      - pattern: |
          router.push($URL)
      - pattern: |
          router.replace($URL)
      - pattern: |
          redirect($URL)
    message: '确保重定向URL经过验证，防止开放重定向攻击'
    languages: [typescript, javascript]
    severity: WARNING
    metadata:
      category: security
      technology: [nextjs]
      cwe: 'CWE-601: URL Redirection to Untrusted Site'
      owasp: 'A01:2021 - Broken Access Control'

  - id: insecure-random-generation
    pattern-either:
      - pattern: Math.random()
      - pattern: new Date().getTime()
      - pattern: Date.now()
    message: '避免使用不安全的随机数生成方法，使用crypto.randomBytes()或crypto.getRandomValues()'
    languages: [typescript, javascript]
    severity: WARNING
    metadata:
      category: security
      technology: [nodejs]
      cwe:
        'CWE-338: Use of Cryptographically Weak Pseudo-Random Number Generator'
      owasp: 'A02:2021 - Cryptographic Failures'

  - id: nextjs-unsafe-html-injection
    pattern-either:
      - pattern: |
          $EL.innerHTML = $VAR
      - pattern: |
          $EL.outerHTML = $VAR
    message: '避免直接设置innerHTML/outerHTML，存在XSS风险'
    languages: [typescript, javascript]
    severity: ERROR
    metadata:
      category: security
      technology: [nextjs, react]
      cwe: 'CWE-79: Cross-site Scripting (XSS)'
      owasp: 'A03:2021 - Injection'

  - id: weak-crypto-algorithm
    pattern-either:
      - pattern: crypto.createHash('md5')
      - pattern: crypto.createHash('sha1')
      - pattern: crypto.createHmac('md5', $KEY)
      - pattern: crypto.createHmac('sha1', $KEY)
    message: '避免使用弱加密算法MD5和SHA1，推荐使用SHA-256或更强的算法'
    languages: [typescript, javascript]
    severity: ERROR
    metadata:
      category: security
      technology: [nodejs]
      cwe: 'CWE-327: Use of a Broken or Risky Cryptographic Algorithm'
      owasp: 'A02:2021 - Cryptographic Failures'

  - id: sql-injection-risk
    pattern-either:
      - pattern: |
          $DB.query($QUERY + $VAR)
      - pattern: |
          $DB.execute($QUERY + $VAR)
      - pattern: |
          $DB.raw($QUERY + $VAR)
    message: '可能存在SQL注入风险，使用参数化查询或ORM'
    languages: [typescript, javascript]
    severity: ERROR
    metadata:
      category: security
      technology: [nodejs, database]
      cwe: 'CWE-89: SQL Injection'
      owasp: 'A03:2021 - Injection'

  - id: nextjs-unsafe-server-action
    pattern: |
      export async function $FUNC(...$ARGS) {
        ...
        eval($VAR)
        ...
      }
    message: 'Server Actions中避免使用eval()，存在服务器端代码注入风险'
    languages: [typescript, javascript]
    severity: ERROR
    metadata:
      category: security
      technology: [nextjs]
      cwe:
        'CWE-95: Improper Neutralization of Directives in Dynamically Evaluated
        Code'
      owasp: 'A03:2021 - Injection'

  - id: environment-variable-exposure
    pattern-regex: "console\\.(log|info|debug|warn|error)\\(.*process\\.env\\..*\\)"
    message: '避免在日志中暴露环境变量，可能包含敏感信息'
    languages: [typescript, javascript]
    severity: WARNING
    metadata:
      category: security
      technology: [nodejs]
      cwe: 'CWE-532: Insertion of Sensitive Information into Log File'
      owasp: 'A09:2021 - Security Logging and Monitoring Failures'
