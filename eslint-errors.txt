
> tucsenberg-web-frontier@0.1.0 lint:check
> eslint . --ext .js,.jsx,.ts,.tsx --config eslint.config.mjs


/Users/<USER>/Warehouse/Focus/tucsenberg-web-frontier/mdx-components.tsx
  10:8  error    Function 'useMDXComponents' has too many lines (88). Maximum allowed is 80                                   max-lines-per-function
  70:7  warning  Image elements must have an alt prop, either with meaningful text, or an empty string for decorative images  jsx-a11y/alt-text

/Users/<USER>/Warehouse/Focus/tucsenberg-web-frontier/middleware.ts
  52:40  error  No magic number: 0.9  no-magic-numbers
  52:46  error  No magic number: 0.7  no-magic-numbers

/Users/<USER>/Warehouse/Focus/tucsenberg-web-frontier/sentry.edge.config.ts
  74:25  error  ["request"] is better written in dot notation  dot-notation

/Users/<USER>/Warehouse/Focus/tucsenberg-web-frontier/sentry.server.config.ts
  80:25  error  ["request"] is better written in dot notation  dot-notation

/Users/<USER>/Warehouse/Focus/tucsenberg-web-frontier/src/app/api/test-content/route.ts
   5:8  error  Async function 'GET' has too many lines (82). Maximum allowed is 80  max-lines-per-function
   5:8  error  Async function 'GET' has no 'await' expression                       require-await
   5:8  error  Async function 'GET' has a complexity of 15. Maximum allowed is 10   complexity
  76:5  error  Unexpected console statement                                         no-console

/Users/<USER>/Warehouse/Focus/tucsenberg-web-frontier/src/constants/i18n-constants.ts
   13:11  error  No magic number: 60    no-magic-numbers
   13:16  error  No magic number: 1000  no-magic-numbers
   15:9   error  No magic number: 60    no-magic-numbers
   15:14  error  No magic number: 60    no-magic-numbers
   15:19  error  No magic number: 1000  no-magic-numbers
   17:8   error  No magic number: 24    no-magic-numbers
   17:13  error  No magic number: 60    no-magic-numbers
   17:18  error  No magic number: 60    no-magic-numbers
   17:23  error  No magic number: 1000  no-magic-numbers
   19:9   error  No magic number: 365   no-magic-numbers
   19:15  error  No magic number: 24    no-magic-numbers
   19:20  error  No magic number: 60    no-magic-numbers
   19:25  error  No magic number: 60    no-magic-numbers
   19:30  error  No magic number: 1000  no-magic-numbers
   27:22  error  No magic number: 5     no-magic-numbers
   29:22  error  No magic number: 30    no-magic-numbers
  151:27  error  No magic number: 30    no-magic-numbers
  155:22  error  No magic number: 10    no-magic-numbers
  155:27  error  No magic number: 1024  no-magic-numbers
  155:34  error  No magic number: 1024  no-magic-numbers
  169:23  error  No magic number: 5     no-magic-numbers

/Users/<USER>/Warehouse/Focus/tucsenberg-web-frontier/src/hooks/__tests__/use-enhanced-theme.test.ts
   28:30  error  Arrow function has too many lines (399). Maximum allowed is 80  max-lines-per-function
   42:40  error  No magic number: 1000                                           no-magic-numbers
   50:27  error  Unexpected any. Specify a different type                        @typescript-eslint/no-explicit-any
   70:37  error  Too many nested callbacks (4). Maximum allowed is 3             max-nested-callbacks
   83:37  error  Too many nested callbacks (4). Maximum allowed is 3             max-nested-callbacks
   95:37  error  Too many nested callbacks (4). Maximum allowed is 3             max-nested-callbacks
  107:37  error  Too many nested callbacks (4). Maximum allowed is 3             max-nested-callbacks
  119:37  error  Too many nested callbacks (4). Maximum allowed is 3             max-nested-callbacks
  127:29  error  Unexpected any. Specify a different type                        @typescript-eslint/no-explicit-any
  130:37  error  Too many nested callbacks (4). Maximum allowed is 3             max-nested-callbacks
  136:22  error  Arrow function has too many lines (170). Maximum allowed is 80  max-lines-per-function
  138:37  error  Too many nested callbacks (4). Maximum allowed is 3             max-nested-callbacks
  140:17  error  Too many nested callbacks (4). Maximum allowed is 3             max-nested-callbacks
  140:26  error  Async arrow function has no 'await' expression                  require-await
  148:9   error  No magic number: 1000                                           no-magic-numbers
  149:9   error  No magic number: 1000                                           no-magic-numbers
  166:37  error  Too many nested callbacks (4). Maximum allowed is 3             max-nested-callbacks
  168:17  error  Too many nested callbacks (4). Maximum allowed is 3             max-nested-callbacks
  168:26  error  Async arrow function has no 'await' expression                  require-await
  177:9   error  No magic number: 1000                                           no-magic-numbers
  178:9   error  No magic number: 1000                                           no-magic-numbers
  186:29  error  Too many nested callbacks (4). Maximum allowed is 3             max-nested-callbacks
  201:37  error  Too many nested callbacks (4). Maximum allowed is 3             max-nested-callbacks
  203:17  error  Too many nested callbacks (4). Maximum allowed is 3             max-nested-callbacks
  203:26  error  Async arrow function has no 'await' expression                  require-await
  220:68  error  Too many nested callbacks (4). Maximum allowed is 3             max-nested-callbacks
  230:37  error  Too many nested callbacks (4). Maximum allowed is 3             max-nested-callbacks
  233:17  error  Too many nested callbacks (4). Maximum allowed is 3             max-nested-callbacks
  233:26  error  Async arrow function has no 'await' expression                  require-await
  234:16  error  Too many nested callbacks (5). Maximum allowed is 3             max-nested-callbacks
  251:37  error  Too many nested callbacks (4). Maximum allowed is 3             max-nested-callbacks
  254:17  error  Too many nested callbacks (4). Maximum allowed is 3             max-nested-callbacks
  254:26  error  Async arrow function has no 'await' expression                  require-await
  255:16  error  Too many nested callbacks (5). Maximum allowed is 3             max-nested-callbacks
  272:37  error  Too many nested callbacks (4). Maximum allowed is 3             max-nested-callbacks
  275:17  error  Too many nested callbacks (4). Maximum allowed is 3             max-nested-callbacks
  275:26  error  Async arrow function has no 'await' expression                  require-await
  276:16  error  Too many nested callbacks (5). Maximum allowed is 3             max-nested-callbacks
  294:37  error  Too many nested callbacks (4). Maximum allowed is 3             max-nested-callbacks
  297:17  error  Too many nested callbacks (4). Maximum allowed is 3             max-nested-callbacks
  297:26  error  Async arrow function has no 'await' expression                  require-await
  298:16  error  Too many nested callbacks (5). Maximum allowed is 3             max-nested-callbacks
  307:22  error  Arrow function has too many lines (91). Maximum allowed is 80   max-lines-per-function
  328:37  error  Too many nested callbacks (4). Maximum allowed is 3             max-nested-callbacks
  333:12  error  'React' is not defined                                          no-undef
  335:17  error  Too many nested callbacks (4). Maximum allowed is 3             max-nested-callbacks
  335:26  error  Async arrow function has no 'await' expression                  require-await
  377:37  error  Too many nested callbacks (4). Maximum allowed is 3             max-nested-callbacks
  379:17  error  Too many nested callbacks (4). Maximum allowed is 3             max-nested-callbacks
  379:26  error  Async arrow function has no 'await' expression                  require-await
  406:37  error  Too many nested callbacks (4). Maximum allowed is 3             max-nested-callbacks
  409:14  error  Too many nested callbacks (4). Maximum allowed is 3             max-nested-callbacks
  419:37  error  Too many nested callbacks (4). Maximum allowed is 3             max-nested-callbacks
  421:14  error  Too many nested callbacks (4). Maximum allowed is 3             max-nested-callbacks

/Users/<USER>/Warehouse/Focus/tucsenberg-web-frontier/src/hooks/use-theme-toggle.ts
  13:8  error    Function 'useThemeToggle' has too many lines (84). Maximum allowed is 80                                        max-lines-per-function
  31:5  error    Avoid initializing state in an effect. Instead, pass "mounted"'s initial value to its `useState`                react-you-might-not-need-an-effect/no-initialize-state
  72:5  warning  React Hook useCallback has a missing dependency: 'setIsOpen'. Either include it or remove the dependency array  react-hooks/exhaustive-deps

/Users/<USER>/Warehouse/Focus/tucsenberg-web-frontier/src/lib/__tests__/accessibility.test.ts
   33:34  error  Arrow function has too many lines (528). Maximum allowed is 80  max-lines-per-function
   48:10  error  Unexpected any. Specify a different type                        @typescript-eslint/no-explicit-any
   60:24  error  Arrow function has too many lines (87). Maximum allowed is 80   max-lines-per-function
   62:7   error  Do not use 'new' for side effects                               no-new
   85:7   error  Do not use 'new' for side effects                               no-new
   94:44  error  Too many nested callbacks (4). Maximum allowed is 3             max-nested-callbacks
   99:14  error  Too many nested callbacks (4). Maximum allowed is 3             max-nested-callbacks
  113:42  error  Too many nested callbacks (4). Maximum allowed is 3             max-nested-callbacks
  118:14  error  Too many nested callbacks (4). Maximum allowed is 3             max-nested-callbacks
  144:14  error  Too many nested callbacks (4). Maximum allowed is 3             max-nested-callbacks
  148:24  error  Arrow function has too many lines (161). Maximum allowed is 80  max-lines-per-function
  154:18  error  Too many nested callbacks (4). Maximum allowed is 3             max-nested-callbacks
  157:10  error  No magic number: 150                                            no-magic-numbers
  165:18  error  Too many nested callbacks (4). Maximum allowed is 3             max-nested-callbacks
  168:10  error  No magic number: 150                                            no-magic-numbers
  176:18  error  Too many nested callbacks (4). Maximum allowed is 3             max-nested-callbacks
  179:10  error  No magic number: 150                                            no-magic-numbers
  194:19  error  Unexpected any. Specify a different type                        @typescript-eslint/no-explicit-any
  196:14  error  Too many nested callbacks (4). Maximum allowed is 3             max-nested-callbacks
  202:33  error  Unexpected any. Specify a different type                        @typescript-eslint/no-explicit-any
  204:14  error  Too many nested callbacks (4). Maximum allowed is 3             max-nested-callbacks
  206:18  error  Too many nested callbacks (4). Maximum allowed is 3             max-nested-callbacks
  208:10  error  No magic number: 150                                            no-magic-numbers
  217:29  error  Too many nested callbacks (4). Maximum allowed is 3             max-nested-callbacks
  217:35  error  Unexpected empty arrow function                                 no-empty-function
  240:10  error  No magic number: 200                                            no-magic-numbers
  251:10  error  No magic number: 150                                            no-magic-numbers
  263:10  error  No magic number: 150                                            no-magic-numbers
  275:10  error  No magic number: 150                                            no-magic-numbers
  302:20  error  Too many nested callbacks (4). Maximum allowed is 3             max-nested-callbacks
  305:12  error  No magic number: 1100                                           no-magic-numbers
  306:10  error  No magic number: 150                                            no-magic-numbers
  310:22  error  Arrow function has too many lines (122). Maximum allowed is 80  max-lines-per-function
  488:12  error  Unexpected any. Specify a different type                        @typescript-eslint/no-explicit-any
  501:1   error  File has too many lines (604). Maximum allowed is 500           max-lines
  501:12  error  Unexpected any. Specify a different type                        @typescript-eslint/no-explicit-any
  515:12  error  Unexpected any. Specify a different type                        @typescript-eslint/no-explicit-any
  533:12  error  Unexpected any. Specify a different type                        @typescript-eslint/no-explicit-any

/Users/<USER>/Warehouse/Focus/tucsenberg-web-frontier/src/lib/__tests__/colors.test.ts
   13:23  error  Arrow function has too many lines (635). Maximum allowed is 80  max-lines-per-function
   14:26  error  Arrow function has too many lines (112). Maximum allowed is 80  max-lines-per-function
  127:33  error  Arrow function has too many lines (124). Maximum allowed is 80  max-lines-per-function
  135:40  error  No magic number: 20                                             no-magic-numbers
  145:37  error  No magic number: 2                                              no-magic-numbers
  170:14  error  Too many nested callbacks (4). Maximum allowed is 3             max-nested-callbacks
  173:14  error  Too many nested callbacks (4). Maximum allowed is 3             max-nested-callbacks
  182:40  error  No magic number: 20                                             no-magic-numbers
  190:14  error  Too many nested callbacks (4). Maximum allowed is 3             max-nested-callbacks
  199:14  error  Too many nested callbacks (4). Maximum allowed is 3             max-nested-callbacks
  209:39  error  No magic number: 5                                              no-magic-numbers
  217:14  error  Too many nested callbacks (4). Maximum allowed is 3             max-nested-callbacks
  225:14  error  Too many nested callbacks (4). Maximum allowed is 3             max-nested-callbacks
  233:14  error  Too many nested callbacks (4). Maximum allowed is 3             max-nested-callbacks
  244:14  error  Too many nested callbacks (4). Maximum allowed is 3             max-nested-callbacks
  252:39  error  Arrow function has too many lines (147). Maximum allowed is 80  max-lines-per-function
  296:14  error  Too many nested callbacks (4). Maximum allowed is 3             max-nested-callbacks
  299:14  error  Too many nested callbacks (4). Maximum allowed is 3             max-nested-callbacks
  309:14  error  Too many nested callbacks (4). Maximum allowed is 3             max-nested-callbacks
  318:14  error  Too many nested callbacks (4). Maximum allowed is 3             max-nested-callbacks
  323:14  error  Too many nested callbacks (4). Maximum allowed is 3             max-nested-callbacks
  328:14  error  Too many nested callbacks (4). Maximum allowed is 3             max-nested-callbacks
  337:14  error  Too many nested callbacks (4). Maximum allowed is 3             max-nested-callbacks
  340:14  error  Too many nested callbacks (4). Maximum allowed is 3             max-nested-callbacks
  353:14  error  Too many nested callbacks (4). Maximum allowed is 3             max-nested-callbacks
  356:14  error  Too many nested callbacks (4). Maximum allowed is 3             max-nested-callbacks
  367:14  error  Too many nested callbacks (4). Maximum allowed is 3             max-nested-callbacks
  377:14  error  Too many nested callbacks (4). Maximum allowed is 3             max-nested-callbacks
  379:14  error  Too many nested callbacks (4). Maximum allowed is 3             max-nested-callbacks
  383:14  error  Too many nested callbacks (4). Maximum allowed is 3             max-nested-callbacks
  391:14  error  Too many nested callbacks (4). Maximum allowed is 3             max-nested-callbacks
  394:14  error  Too many nested callbacks (4). Maximum allowed is 3             max-nested-callbacks
  430:52  error  Unexpected any. Specify a different type                        @typescript-eslint/no-explicit-any
  448:37  error  Arrow function has too many lines (93). Maximum allowed is 80   max-lines-per-function
  501:1   error  File has too many lines (647). Maximum allowed is 500           max-lines
  569:48  error  Too many nested callbacks (4). Maximum allowed is 3             max-nested-callbacks
  574:45  error  No magic number: 360                                            no-magic-numbers
  584:47  error  Too many nested callbacks (4). Maximum allowed is 3             max-nested-callbacks
  589:45  error  No magic number: 360                                            no-magic-numbers
  601:48  error  Too many nested callbacks (4). Maximum allowed is 3             max-nested-callbacks
  605:47  error  Too many nested callbacks (4). Maximum allowed is 3             max-nested-callbacks
  616:14  error  Too many nested callbacks (4). Maximum allowed is 3             max-nested-callbacks
  617:14  error  Too many nested callbacks (4). Maximum allowed is 3             max-nested-callbacks
  618:14  error  Too many nested callbacks (4). Maximum allowed is 3             max-nested-callbacks
  624:14  error  Too many nested callbacks (4). Maximum allowed is 3             max-nested-callbacks
  631:14  error  Too many nested callbacks (4). Maximum allowed is 3             max-nested-callbacks
  632:14  error  Too many nested callbacks (4). Maximum allowed is 3             max-nested-callbacks

/Users/<USER>/Warehouse/Focus/tucsenberg-web-frontier/src/lib/__tests__/theme-analytics.test.ts
   24:28  error  Arrow function has too many lines (613). Maximum allowed is 80  max-lines-per-function
   44:45  error  No magic number: 1000                                           no-magic-numbers
   71:7   error  Do not use 'new' for side effects                               no-new
   79:60  error  No magic number: 1000                                           no-magic-numbers
   79:66  error  No magic number: 1100                                           no-magic-numbers
   95:21  error  Arrow function has too many lines (142). Maximum allowed is 80  max-lines-per-function
  102:62  error  No magic number: 1000                                           no-magic-numbers
  102:68  error  No magic number: 1100                                           no-magic-numbers
  108:42  error  No magic number: 0x80000000                                     no-magic-numbers
  109:46  error  Too many nested callbacks (4). Maximum allowed is 3             max-nested-callbacks
  110:9   error  Use array destructuring                                         prefer-destructuring
  119:54  error  No magic number: 1000                                           no-magic-numbers
  119:60  error  No magic number: 1100                                           no-magic-numbers
  130:50  error  No magic number: 0.3                                            no-magic-numbers
  137:54  error  No magic number: 1000                                           no-magic-numbers
  137:60  error  No magic number: 1100                                           no-magic-numbers
  139:14  error  detect Math.random()                                            security-node/detect-insecure-randomness
  156:46  error  Too many nested callbacks (4). Maximum allowed is 3             max-nested-callbacks
  161:14  error  Too many nested callbacks (4). Maximum allowed is 3             max-nested-callbacks
  162:56  error  No magic number: 1000                                           no-magic-numbers
  162:62  error  No magic number: 1100                                           no-magic-numbers
  170:48  error  Too many nested callbacks (4). Maximum allowed is 3             max-nested-callbacks
  179:52  error  Unexpected any. Specify a different type                        @typescript-eslint/no-explicit-any
  181:50  error  No magic number: 0.3                                            no-magic-numbers
  188:54  error  No magic number: 1000                                           no-magic-numbers
  188:60  error  No magic number: 1100                                           no-magic-numbers
  190:14  error  detect Math.random()                                            security-node/detect-insecure-randomness
  203:56  error  No magic number: 1000                                           no-magic-numbers
  203:62  error  No magic number: 1100                                           no-magic-numbers
  213:56  error  No magic number: 1000                                           no-magic-numbers
  213:62  error  No magic number: 1100                                           no-magic-numbers
  223:60  error  No magic number: 1000                                           no-magic-numbers
  223:66  error  No magic number: 1100                                           no-magic-numbers
  233:56  error  No magic number: 1000                                           no-magic-numbers
  233:62  error  No magic number: 1100                                           no-magic-numbers
  240:52  error  No magic number: 1000                                           no-magic-numbers
  240:58  error  No magic number: 1150                                           no-magic-numbers
  255:52  error  No magic number: 1000                                           no-magic-numbers
  255:58  error  No magic number: 1100                                           no-magic-numbers
  256:52  error  No magic number: 1200                                           no-magic-numbers
  256:58  error  No magic number: 1300                                           no-magic-numbers
  260:34  error  No magic number: 2                                              no-magic-numbers
  263:36  error  Too many nested callbacks (4). Maximum allowed is 3             max-nested-callbacks
  264:37  error  Too many nested callbacks (4). Maximum allowed is 3             max-nested-callbacks
  282:52  error  No magic number: 1000                                           no-magic-numbers
  282:58  error  No magic number: 1250                                           no-magic-numbers
  312:14  error  Too many nested callbacks (4). Maximum allowed is 3             max-nested-callbacks
  313:54  error  No magic number: 1000                                           no-magic-numbers
  313:60  error  No magic number: 1100                                           no-magic-numbers
  321:52  error  No magic number: 1000                                           no-magic-numbers
  321:58  error  No magic number: 1050                                           no-magic-numbers
  322:53  error  No magic number: 1100                                           no-magic-numbers
  322:59  error  No magic number: 1200                                           no-magic-numbers
  323:54  error  No magic number: 1300                                           no-magic-numbers
  323:60  error  No magic number: 1450                                           no-magic-numbers
  354:25  error  Arrow function has too many lines (160). Maximum allowed is 80  max-lines-per-function
  356:41  error  No magic number: 25                                             no-magic-numbers
  356:46  error  No magic number: 60                                             no-magic-numbers
  356:51  error  No magic number: 60                                             no-magic-numbers
  356:56  error  No magic number: 1000                                           no-magic-numbers
  364:24  error  No magic number: 100                                            no-magic-numbers
  369:52  error  No magic number: 1000                                           no-magic-numbers
  369:58  error  No magic number: 1100                                           no-magic-numbers
  377:27  error  No magic number: 1005                                           no-magic-numbers
  378:58  error  No magic number: 100                                            no-magic-numbers
  378:67  error  No magic number: 100                                            no-magic-numbers
  378:73  error  No magic number: 50                                             no-magic-numbers
  382:57  error  No magic number: 1000                                           no-magic-numbers
  393:57  error  No magic number: 1000                                           no-magic-numbers
  393:63  error  No magic number: 1100                                           no-magic-numbers
  404:50  error  Too many nested callbacks (4). Maximum allowed is 3             max-nested-callbacks
  405:9   error  Unary operator '++' used                                        no-plusplus
  406:25  error  No magic number: 2                                              no-magic-numbers
  414:14  error  Too many nested callbacks (4). Maximum allowed is 3             max-nested-callbacks
  415:54  error  No magic number: 1000                                           no-magic-numbers
  415:60  error  No magic number: 1100                                           no-magic-numbers
  421:52  error  No magic number: 1000                                           no-magic-numbers
  421:58  error  No magic number: 1100                                           no-magic-numbers
  424:42  error  Unexpected any. Specify a different type                        @typescript-eslint/no-explicit-any
  436:14  error  Too many nested callbacks (4). Maximum allowed is 3             max-nested-callbacks
  437:54  error  No magic number: 2000                                           no-magic-numbers
  437:60  error  No magic number: 2100                                           no-magic-numbers
  449:27  error  No magic number: 2000                                           no-magic-numbers
  453:15  error  No magic number: 100                                            no-magic-numbers
  454:15  error  No magic number: 100                                            no-magic-numbers
  454:21  error  No magic number: 50                                             no-magic-numbers
  459:57  error  No magic number: 1000                                           no-magic-numbers
  462:42  error  Unexpected any. Specify a different type                        @typescript-eslint/no-explicit-any
  463:50  error  No magic number: 1000                                           no-magic-numbers
  466:65  error  No magic number: 50                                             no-magic-numbers
  472:27  error  No magic number: 1005                                           no-magic-numbers
  473:58  error  No magic number: 100                                            no-magic-numbers
  473:67  error  No magic number: 100                                            no-magic-numbers
  473:73  error  No magic number: 50                                             no-magic-numbers
  479:33  error  Too many nested callbacks (4). Maximum allowed is 3             max-nested-callbacks
  484:14  error  Too many nested callbacks (4). Maximum allowed is 3             max-nested-callbacks
  485:54  error  No magic number: 200000                                         no-magic-numbers
  485:62  error  No magic number: 200100                                         no-magic-numbers
  494:27  error  No magic number: 1005                                           no-magic-numbers
  495:58  error  No magic number: 100                                            no-magic-numbers
  495:67  error  No magic number: 100                                            no-magic-numbers
  495:73  error  No magic number: 50                                             no-magic-numbers
  501:1   error  File has too many lines (636). Maximum allowed is 500           max-lines
  501:33  error  Too many nested callbacks (4). Maximum allowed is 3             max-nested-callbacks
  506:14  error  Too many nested callbacks (4). Maximum allowed is 3             max-nested-callbacks
  507:54  error  No magic number: 200000                                         no-magic-numbers
  507:62  error  No magic number: 200100                                         no-magic-numbers
  517:52  error  No magic number: 1000                                           no-magic-numbers
  517:58  error  No magic number: 1100                                           no-magic-numbers
  518:52  error  No magic number: 1200                                           no-magic-numbers
  518:58  error  No magic number: 1300                                           no-magic-numbers
  519:52  error  No magic number: 1400                                           no-magic-numbers
  519:58  error  No magic number: 1500                                           no-magic-numbers
  522:38  error  Unexpected any. Specify a different type                        @typescript-eslint/no-explicit-any
  524:37  error  No magic number: 2                                              no-magic-numbers
  527:11  error  Too many nested callbacks (4). Maximum allowed is 3             max-nested-callbacks
  527:15  error  Unexpected any. Specify a different type                        @typescript-eslint/no-explicit-any
  529:14  error  No magic number: 2                                              no-magic-numbers
  553:52  error  No magic number: 1000                                           no-magic-numbers
  553:58  error  No magic number: 1100                                           no-magic-numbers
  569:9   error  No magic number: 100                                            no-magic-numbers
  573:9   error  No magic number: 100                                            no-magic-numbers
  592:14  error  Too many nested callbacks (4). Maximum allowed is 3             max-nested-callbacks
  603:14  error  Too many nested callbacks (4). Maximum allowed is 3             max-nested-callbacks
  604:54  error  No magic number: 1000                                           no-magic-numbers
  604:60  error  No magic number: 1100                                           no-magic-numbers
  613:14  error  Too many nested callbacks (4). Maximum allowed is 3             max-nested-callbacks
  614:54  error  No magic number: 1000                                           no-magic-numbers
  614:60  error  No magic number: 1100                                           no-magic-numbers
  622:52  error  No magic number: 1000                                           no-magic-numbers
  622:58  error  No magic number: 1000                                           no-magic-numbers
  630:52  error  No magic number: 1100                                           no-magic-numbers
  630:58  error  No magic number: 1000                                           no-magic-numbers
  633:42  error  No magic number: -100                                           no-magic-numbers

/Users/<USER>/Warehouse/Focus/tucsenberg-web-frontier/src/lib/accessibility.ts
  111:11  error  Unexpected console statement  no-console
  124:15  error  Unexpected console statement  no-console
  147:7   error  Unexpected console statement  no-console
  168:7   error  Unexpected console statement  no-console
  187:7   error  Unexpected console statement  no-console
  206:7   error  Unexpected console statement  no-console
  234:7   error  Unexpected console statement  no-console
  303:7   error  Unsafe Regular Expression     security/detect-unsafe-regex

/Users/<USER>/Warehouse/Focus/tucsenberg-web-frontier/src/lib/content.ts
   60:5   error  Unexpected console statement                                                      no-console
   68:8   error  Function 'validateContentMetadata' has a complexity of 17. Maximum allowed is 10  complexity
   69:13  error  Unexpected any. Specify a different type                                          @typescript-eslint/no-explicit-any
  106:59  error  No magic number: 60                                                               no-magic-numbers
  109:71  error  No magic number: 160                                                              no-magic-numbers
  129:25  error  Found readFileSync from package "fs" with non literal argument at index 0         security/detect-non-literal-fs-filename
  140:27  error  ["slug"] is better written in dot notation                                        dot-notation
  154:7   error  Unexpected console statement                                                      no-console
  182:8   error  Found existsSync from package "fs" with non literal argument at index 0           security/detect-non-literal-fs-filename
  186:10  error  Found readdirSync from package "fs" with non literal argument at index 0          security/detect-non-literal-fs-filename
  202:13  error  Arrow function has a complexity of 11. Maximum allowed is 10                      complexity
  244:20  error  Generic Object Injection Sink                                                     security/detect-object-injection
  245:20  error  Generic Object Injection Sink                                                     security/detect-object-injection
  297:7   error  Unexpected console statement                                                      no-console
  336:5   error  Generic Object Injection Sink                                                     security/detect-object-injection
  337:5   error  Generic Object Injection Sink                                                     security/detect-object-injection
  344:7   error  Generic Object Injection Sink                                                     security/detect-object-injection
  347:7   error  Unexpected console statement                                                      no-console
  355:7   error  Generic Object Injection Sink                                                     security/detect-object-injection
  358:7   error  Unexpected console statement                                                      no-console
  374:7   error  Unexpected console statement                                                      no-console

✖ 336 problems (334 errors, 2 warnings)
  3 errors and 0 warnings potentially fixable with the `--fix` option.

