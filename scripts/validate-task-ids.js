#!/usr/bin/env node

/**
 * 任务ID验证和修复工具
 * 检查所有任务ID是否符合UUID v4标准，并提供修复建议
 */

const crypto = require('crypto');

// UUID v4 正则表达式
const UUID_V4_REGEX =
  /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;

/**
 * 生成标准UUID v4
 */
function generateUUIDv4() {
  return crypto.randomUUID();
}

/**
 * 验证ID是否为有效的UUID v4
 */
function isValidUUIDv4(id) {
  return UUID_V4_REGEX.test(id);
}

/**
 * 从任务列表输出中提取任务ID
 */
function extractTaskIds(taskListOutput) {
  const idPattern = /\*\*ID:\*\* `([^`]+)`/g;
  const ids = [];
  let match;

  while ((match = idPattern.exec(taskListOutput)) !== null) {
    ids.push(match[1]);
  }

  return ids;
}

/**
 * 分析任务ID问题
 */
function analyzeTaskIds(ids) {
  const results = {
    valid: [],
    invalid: [],
    suggestions: new Map(),
  };

  ids.forEach((id) => {
    if (isValidUUIDv4(id)) {
      results.valid.push(id);
    } else {
      results.invalid.push(id);
      results.suggestions.set(id, generateUUIDv4());
    }
  });

  return results;
}

/**
 * 生成修复报告
 */
function generateReport(analysis) {
  console.log('🔍 任务ID验证报告');
  console.log('='.repeat(50));

  console.log(`\n✅ 有效的UUID v4 ID: ${analysis.valid.length}个`);
  analysis.valid.forEach((id) => {
    console.log(`   ${id}`);
  });

  console.log(`\n❌ 无效的ID: ${analysis.invalid.length}个`);
  analysis.invalid.forEach((id) => {
    console.log(`   ${id}`);
  });

  if (analysis.invalid.length > 0) {
    console.log('\n🔧 修复建议:');
    console.log('-'.repeat(30));
    analysis.suggestions.forEach((newId, oldId) => {
      console.log(`${oldId} → ${newId}`);
    });

    console.log('\n📋 修复命令模板:');
    analysis.suggestions.forEach((newId, oldId) => {
      console.log(`# 更新任务ID: ${oldId}`);
      console.log(`# 新ID: ${newId}`);
      console.log('');
    });
  }

  return analysis;
}

// 如果直接运行此脚本
if (require.main === module) {
  // 示例任务ID列表（实际使用时应该从任务管理系统获取）
  const sampleIds = [
    'b917caf6-5050-44a6-aaa0-54f918cb9842', // 有效
    'a1b2c3d4-e5f6-7890-abcd-ef1234567890', // 无效
    'sentry-monitoring-core-2025-0728', // 无效
    'gh-actions-001-2025-0728', // 无效
    '95af7988-2481-45b9-9090-1afb4db2d43a', // 有效
  ];

  console.log('🚀 开始验证任务ID...\n');
  const analysis = analyzeTaskIds(sampleIds);
  generateReport(analysis);
}

module.exports = {
  generateUUIDv4,
  isValidUUIDv4,
  extractTaskIds,
  analyzeTaskIds,
  generateReport,
};
