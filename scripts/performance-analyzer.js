#!/usr/bin/env node

/**
 * 性能分析器 - 监控打包大小、加载时间和内存使用
 * Performance Analyzer - Monitor bundle size, load time and memory usage
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

class PerformanceAnalyzer {
  constructor() {
    this.report = {
      timestamp: new Date().toISOString(),
      performance: {
        bundleSize: {},
        loadTime: {},
        memoryUsage: {},
        score: 0,
      },
      limits: {
        mainBundle: 50 * 1024, // 50KB
        totalBundle: 260 * 1024, // 260KB
        loadTime: 3000, // 3秒
        memoryUsage: 50 * 1024 * 1024, // 50MB
      },
      passed: true,
      issues: [],
    };
  }

  /**
   * 分析打包文件大小
   */
  analyzeBundleSize() {
    try {
      console.log('📦 分析打包文件大小...');
      
      const nextDir = path.join(process.cwd(), '.next');
      if (!fs.existsSync(nextDir)) {
        throw new Error('未找到构建文件，请先运行 pnpm build');
      }

      // 分析静态文件大小
      const staticDir = path.join(nextDir, 'static');
      if (fs.existsSync(staticDir)) {
        const chunks = this.getChunkSizes(staticDir);
        this.report.performance.bundleSize = chunks;

        // 检查主包大小
        const mainBundleSize = chunks.main || 0;
        if (mainBundleSize > this.report.limits.mainBundle) {
          this.report.passed = false;
          this.report.issues.push({
            type: 'bundle-size',
            severity: 'high',
            message: `主包大小 ${this.formatBytes(mainBundleSize)} 超出限制 ${this.formatBytes(this.report.limits.mainBundle)}`,
          });
        }

        // 检查总包大小
        const totalSize = Object.values(chunks).reduce((sum, size) => sum + size, 0);
        if (totalSize > this.report.limits.totalBundle) {
          this.report.passed = false;
          this.report.issues.push({
            type: 'bundle-size',
            severity: 'medium',
            message: `总包大小 ${this.formatBytes(totalSize)} 超出限制 ${this.formatBytes(this.report.limits.totalBundle)}`,
          });
        }

        console.log(`✅ 主包大小: ${this.formatBytes(mainBundleSize)}`);
        console.log(`✅ 总包大小: ${this.formatBytes(totalSize)}`);
      }
    } catch (error) {
      console.warn(`⚠️  打包大小分析失败: ${error.message}`);
      this.report.issues.push({
        type: 'analysis-error',
        severity: 'low',
        message: `打包大小分析失败: ${error.message}`,
      });
    }
  }

  /**
   * 获取chunk文件大小
   */
  getChunkSizes(staticDir) {
    const chunks = {};
    
    try {
      const jsDir = path.join(staticDir, 'chunks');
      if (fs.existsSync(jsDir)) {
        const files = fs.readdirSync(jsDir);
        
        files.forEach(file => {
          if (file.endsWith('.js')) {
            const filePath = path.join(jsDir, file);
            const stats = fs.statSync(filePath);
            
            if (file.includes('main')) {
              chunks.main = (chunks.main || 0) + stats.size;
            } else if (file.includes('framework')) {
              chunks.framework = (chunks.framework || 0) + stats.size;
            } else {
              chunks.shared = (chunks.shared || 0) + stats.size;
            }
          }
        });
      }
    } catch (error) {
      console.warn(`⚠️  读取chunk文件失败: ${error.message}`);
    }

    return chunks;
  }

  /**
   * 模拟页面加载时间分析
   */
  analyzeLoadTime() {
    try {
      console.log('⏱️  分析页面加载时间...');
      
      // 简单的加载时间估算（基于包大小）
      const bundleSize = Object.values(this.report.performance.bundleSize).reduce((sum, size) => sum + size, 0);
      const estimatedLoadTime = Math.max(1000, bundleSize / 100); // 简单估算公式
      
      this.report.performance.loadTime = {
        estimated: estimatedLoadTime,
        threshold: this.report.limits.loadTime,
      };

      if (estimatedLoadTime > this.report.limits.loadTime) {
        this.report.passed = false;
        this.report.issues.push({
          type: 'load-time',
          severity: 'medium',
          message: `预估加载时间 ${estimatedLoadTime}ms 超出限制 ${this.report.limits.loadTime}ms`,
        });
      }

      console.log(`✅ 预估加载时间: ${estimatedLoadTime}ms`);
    } catch (error) {
      console.warn(`⚠️  加载时间分析失败: ${error.message}`);
    }
  }

  /**
   * 分析内存使用情况
   */
  analyzeMemoryUsage() {
    try {
      console.log('🧠 分析内存使用情况...');
      
      // 获取当前Node.js进程内存使用
      const memUsage = process.memoryUsage();
      this.report.performance.memoryUsage = {
        heapUsed: memUsage.heapUsed,
        heapTotal: memUsage.heapTotal,
        external: memUsage.external,
        rss: memUsage.rss,
      };

      if (memUsage.heapUsed > this.report.limits.memoryUsage) {
        this.report.passed = false;
        this.report.issues.push({
          type: 'memory-usage',
          severity: 'medium',
          message: `内存使用 ${this.formatBytes(memUsage.heapUsed)} 超出限制 ${this.formatBytes(this.report.limits.memoryUsage)}`,
        });
      }

      console.log(`✅ 内存使用: ${this.formatBytes(memUsage.heapUsed)}`);
    } catch (error) {
      console.warn(`⚠️  内存分析失败: ${error.message}`);
    }
  }

  /**
   * 计算性能得分
   */
  calculatePerformanceScore() {
    let score = 100;
    
    // 根据问题严重程度扣分
    this.report.issues.forEach(issue => {
      switch (issue.severity) {
        case 'high':
          score -= 30;
          break;
        case 'medium':
          score -= 15;
          break;
        case 'low':
          score -= 5;
          break;
      }
    });

    this.report.performance.score = Math.max(0, score);
  }

  /**
   * 格式化字节数
   */
  formatBytes(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  /**
   * 生成报告
   */
  generateReport() {
    console.log('\n📊 性能分析报告');
    console.log('='.repeat(40));
    console.log(`⚡ 性能得分: ${this.report.performance.score}/100`);
    console.log(`📦 包大小检查: ${this.report.passed ? '✅ 通过' : '❌ 失败'}`);
    
    if (this.report.issues.length > 0) {
      console.log('\n🚨 发现的问题:');
      this.report.issues.forEach((issue, index) => {
        const icon = issue.severity === 'high' ? '🔴' : issue.severity === 'medium' ? '🟡' : '🟢';
        console.log(`${index + 1}. ${icon} ${issue.message}`);
      });
    }

    // 保存报告
    this.saveReport();

    return this.report.passed;
  }

  /**
   * 保存报告到文件
   */
  saveReport() {
    try {
      const reportsDir = path.join(process.cwd(), 'reports');
      if (!fs.existsSync(reportsDir)) {
        fs.mkdirSync(reportsDir, { recursive: true });
      }

      const reportPath = path.join(reportsDir, 'performance-report.json');
      fs.writeFileSync(reportPath, JSON.stringify(this.report, null, 2));
      
      console.log(`\n📄 性能报告已保存: ${reportPath}`);
    } catch (error) {
      console.warn(`⚠️  保存报告失败: ${error.message}`);
    }
  }

  /**
   * 执行完整的性能分析
   */
  async analyze() {
    console.log('🚀 开始性能分析...\n');
    
    this.analyzeBundleSize();
    this.analyzeLoadTime();
    this.analyzeMemoryUsage();
    this.calculatePerformanceScore();
    
    return this.generateReport();
  }
}

// 命令行接口
if (require.main === module) {
  const analyzer = new PerformanceAnalyzer();
  analyzer.analyze().then(success => {
    process.exit(success ? 0 : 1);
  }).catch(error => {
    console.error(`❌ 性能分析失败: ${error.message}`);
    process.exit(1);
  });
}

module.exports = PerformanceAnalyzer;
