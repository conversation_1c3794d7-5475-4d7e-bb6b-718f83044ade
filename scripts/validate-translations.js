#!/usr/bin/env node

/**
 * 翻译内容一致性验证工具
 * 确保英中文翻译文件结构完全一致，内容质量符合企业标准
 */

const fs = require('fs');
const path = require('path');

console.log('🔍 开始翻译内容一致性验证...\n');

let validationsPassed = 0;
let validationsTotal = 0;
let warnings = [];
let errors = [];

function validate(name, fn) {
  validationsTotal++;
  try {
    const result = fn();
    if (result === true || result === undefined) {
      console.log(`✅ ${name}`);
      validationsPassed++;
    } else if (result.warnings) {
      console.log(`⚠️  ${name}: ${result.warnings.length} warnings`);
      warnings.push(
        ...result.warnings.map((w) => ({ validation: name, warning: w })),
      );
      validationsPassed++;
    }
  } catch (error) {
    console.log(`❌ ${name}: ${error.message}`);
    errors.push({ validation: name, error: error.message });
  }
}

// 验证1: 翻译文件结构完全一致
validate('翻译文件结构完全一致', () => {
  const enMessages = JSON.parse(fs.readFileSync('messages/en.json', 'utf8'));
  const zhMessages = JSON.parse(fs.readFileSync('messages/zh.json', 'utf8'));

  function validateStructure(obj1, obj2, path = '', issues = []) {
    const keys1 = Object.keys(obj1).sort();
    const keys2 = Object.keys(obj2).sort();

    // 检查键的完全一致性
    if (JSON.stringify(keys1) !== JSON.stringify(keys2)) {
      const missing1 = keys2.filter((k) => !keys1.includes(k));
      const missing2 = keys1.filter((k) => !keys2.includes(k));

      if (missing1.length > 0) {
        throw new Error(
          `英文翻译缺少键: ${missing1.map((k) => `${path}.${k}`).join(', ')}`,
        );
      }
      if (missing2.length > 0) {
        throw new Error(
          `中文翻译缺少键: ${missing2.map((k) => `${path}.${k}`).join(', ')}`,
        );
      }
    }

    // 递归验证嵌套结构
    for (const key of keys1) {
      const currentPath = path ? `${path}.${key}` : key;
      const val1 = obj1[key];
      const val2 = obj2[key];

      if (typeof val1 !== typeof val2) {
        throw new Error(
          `类型不匹配: ${currentPath} (${typeof val1} vs ${typeof val2})`,
        );
      }

      if (typeof val1 === 'object' && val1 !== null) {
        validateStructure(val1, val2, currentPath, issues);
      } else if (typeof val1 === 'string') {
        // 验证翻译内容质量
        if (val1.trim() === '' || val2.trim() === '') {
          issues.push(`空翻译: ${currentPath}`);
        }
        if (val1 === val2 && currentPath !== 'home.title') {
          issues.push(`疑似未翻译: ${currentPath} (英中文相同)`);
        }
      }
    }

    return issues;
  }

  const issues = validateStructure(enMessages, zhMessages);
  if (issues.length > 0) {
    return { warnings: issues };
  }
});

// 验证2: 翻译内容企业级质量标准
validate('翻译内容企业级质量标准', () => {
  const enMessages = JSON.parse(fs.readFileSync('messages/en.json', 'utf8'));
  const zhMessages = JSON.parse(fs.readFileSync('messages/zh.json', 'utf8'));

  const qualityIssues = [];

  function checkQuality(obj, locale, path = '') {
    for (const [key, value] of Object.entries(obj)) {
      const currentPath = path ? `${path}.${key}` : key;

      if (typeof value === 'object' && value !== null) {
        checkQuality(value, locale, currentPath);
      } else if (typeof value === 'string') {
        // 检查长度合理性
        if (value.length > 200) {
          qualityIssues.push(
            `${locale}翻译过长: ${currentPath} (${value.length}字符)`,
          );
        }

        // 检查是否包含占位符但未正确格式化
        if (value.includes('{') && !value.match(/\{[^}]+\}/)) {
          qualityIssues.push(`${locale}翻译格式错误: ${currentPath}`);
        }

        // 检查中文翻译是否包含英文标点
        if (locale === 'zh' && value.match(/[.!?;:]/)) {
          qualityIssues.push(`中文翻译包含英文标点: ${currentPath}`);
        }

        // 检查英文翻译首字母大写
        if (
          locale === 'en' &&
          currentPath.includes('title') &&
          value[0] !== value[0].toUpperCase()
        ) {
          qualityIssues.push(`英文标题首字母未大写: ${currentPath}`);
        }
      }
    }
  }

  checkQuality(enMessages, 'en');
  checkQuality(zhMessages, 'zh');

  if (qualityIssues.length > 0) {
    return { warnings: qualityIssues };
  }
});

// 验证3: 关键业务术语一致性
validate('关键业务术语一致性', () => {
  const enMessages = JSON.parse(fs.readFileSync('messages/en.json', 'utf8'));
  const zhMessages = JSON.parse(fs.readFileSync('messages/zh.json', 'utf8'));

  // 定义关键术语映射
  const termMappings = {
    Theme: '主题',
    Language: '语言',
    Loading: '加载',
    Error: '错误',
    Success: '成功',
    Enterprise: '企业',
    Platform: '平台',
  };

  const inconsistencies = [];

  function checkTermConsistency(enObj, zhObj, path = '') {
    for (const [key, enValue] of Object.entries(enObj)) {
      const currentPath = path ? `${path}.${key}` : key;
      const zhValue = zhObj[key];

      if (typeof enValue === 'object' && enValue !== null) {
        if (typeof zhValue === 'object' && zhValue !== null) {
          checkTermConsistency(enValue, zhValue, currentPath);
        }
      } else if (typeof enValue === 'string' && typeof zhValue === 'string') {
        // 检查术语一致性
        for (const [enTerm, zhTerm] of Object.entries(termMappings)) {
          if (enValue.includes(enTerm) && !zhValue.includes(zhTerm)) {
            inconsistencies.push(
              `术语不一致: ${currentPath} - "${enTerm}" 应对应 "${zhTerm}"`,
            );
          }
        }
      }
    }
  }

  checkTermConsistency(enMessages, zhMessages);

  if (inconsistencies.length > 0) {
    return { warnings: inconsistencies };
  }
});

// 验证4: 翻译覆盖率检查
validate('翻译覆盖率检查', () => {
  const enMessages = JSON.parse(fs.readFileSync('messages/en.json', 'utf8'));
  const zhMessages = JSON.parse(fs.readFileSync('messages/zh.json', 'utf8'));

  let totalKeys = 0;
  let translatedKeys = 0;

  function countTranslations(enObj, zhObj, path = '') {
    for (const [key, enValue] of Object.entries(enObj)) {
      const currentPath = path ? `${path}.${key}` : key;
      const zhValue = zhObj[key];

      if (typeof enValue === 'object' && enValue !== null) {
        if (typeof zhValue === 'object' && zhValue !== null) {
          countTranslations(enValue, zhValue, currentPath);
        }
      } else if (typeof enValue === 'string') {
        totalKeys++;
        if (
          typeof zhValue === 'string' &&
          zhValue.trim() !== '' &&
          zhValue !== enValue
        ) {
          translatedKeys++;
        }
      }
    }
  }

  countTranslations(enMessages, zhMessages);

  const coverageRate = (translatedKeys / totalKeys) * 100;

  if (coverageRate < 95) {
    throw new Error(`翻译覆盖率不足: ${coverageRate.toFixed(1)}% (要求≥95%)`);
  }

  console.log(
    `   翻译覆盖率: ${coverageRate.toFixed(1)}% (${translatedKeys}/${totalKeys})`,
  );
});

// 输出验证结果
console.log('\n📊 验证结果:');
console.log(`✅ 通过: ${validationsPassed}/${validationsTotal}`);
console.log(`⚠️  警告: ${warnings.length}`);
console.log(`❌ 错误: ${errors.length}`);

if (warnings.length > 0) {
  console.log('\n⚠️  警告详情:');
  warnings.forEach(({ validation, warning }) => {
    console.log(`  • ${validation}: ${warning}`);
  });
}

if (errors.length > 0) {
  console.log('\n🚨 错误详情:');
  errors.forEach(({ validation, error }) => {
    console.log(`  • ${validation}: ${error}`);
  });
}

const success = errors.length === 0;
const quality =
  warnings.length === 0 ? '优秀' : warnings.length < 5 ? '良好' : '需改进';

console.log(`\n🎯 验证结果: ${success ? '✅ 通过' : '❌ 失败'}`);
console.log(`📈 内容质量: ${quality}`);

process.exit(success ? 0 : 1);
