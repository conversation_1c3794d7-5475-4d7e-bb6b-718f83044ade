#!/usr/bin/env node

/**
 * 修复任务依赖关系工具
 * 根据ID映射表更新所有依赖关系中的旧ID引用
 */

const fs = require('fs');
const path = require('path');

// ID映射表（从task-id-mapping.json读取）
const ID_MAPPING = {
  'a1b2c3d4-e5f6-7890-abcd-ef1234567890':
    '1ea07a45-4606-4217-bb3f-7cd5d26272cf',
  'a1b2c3d4-e5f6-7890-abcd-ef1234567891':
    '03e8d12a-7bce-4cd8-8a2f-a0b2e97c84f4',
  'a1b2c3d4-e5f6-7890-abcd-ef1234567892':
    '78fe619b-179a-44d1-af4d-a1787178f163',
  'a1b2c3d4-e5f6-7890-abcd-ef1234567893':
    '8f8754b6-c724-4022-b630-847f68a0c791',
  'sentry-monitoring-core-2025-0728': 'c0fa19a7-8bc1-48a6-881f-3989314eb4bc',
  'gh-actions-001-2025-0728': '091966ed-0fce-47c3-ac77-96821f45b6fb',
  'renovate-001-2025-0728': '90f73e79-f3cf-49c1-a9e3-2af0ddfe72f8',
  'a2b3c4d5-e6f7-8901-bcde-f23456789012':
    '54c01c15-c217-41a7-b898-9059f28729c4',
};

/**
 * 读取任务数据文件
 */
function loadTaskData() {
  const taskDataPath = path.join(process.cwd(), 'docs/data/tasks.json');

  if (!fs.existsSync(taskDataPath)) {
    console.log('❌ 任务数据文件不存在:', taskDataPath);
    return null;
  }

  try {
    const data = fs.readFileSync(taskDataPath, 'utf8');
    return JSON.parse(data);
  } catch (error) {
    console.log('❌ 读取任务数据文件失败:', error.message);
    return null;
  }
}

/**
 * 修复依赖关系
 */
function fixDependencies(taskData) {
  let fixedCount = 0;
  const fixes = [];

  taskData.tasks.forEach((task) => {
    if (task.dependencies && Array.isArray(task.dependencies)) {
      task.dependencies.forEach((dep) => {
        const oldId = dep.taskId || dep;
        const newId = ID_MAPPING[oldId];

        if (newId) {
          fixes.push({
            taskName: task.name,
            taskId: task.id,
            oldDependency: oldId,
            newDependency: newId,
          });

          // 更新依赖ID
          if (dep.taskId) {
            dep.taskId = newId;
          } else {
            // 如果依赖是字符串格式，需要替换整个数组项
            const index = task.dependencies.indexOf(oldId);
            if (index !== -1) {
              task.dependencies[index] = newId;
            }
          }

          fixedCount++;
        }
      });
    }
  });

  return { fixedCount, fixes, taskData };
}

/**
 * 保存修复后的任务数据
 */
function saveTaskData(taskData) {
  const taskDataPath = path.join(process.cwd(), 'docs/data/tasks.json');
  const backupPath = path.join(process.cwd(), 'docs/data/tasks.json.backup');

  try {
    // 创建备份
    fs.copyFileSync(taskDataPath, backupPath);
    console.log('✅ 已创建备份文件:', backupPath);

    // 保存修复后的数据
    fs.writeFileSync(taskDataPath, JSON.stringify(taskData, null, 2));
    console.log('✅ 已保存修复后的任务数据');

    return true;
  } catch (error) {
    console.log('❌ 保存文件失败:', error.message);
    return false;
  }
}

/**
 * 生成修复报告
 */
function generateFixReport(fixes) {
  console.log('🔧 依赖关系修复报告');
  console.log('='.repeat(60));
  console.log(`修复的依赖关系数量: ${fixes.length}`);

  if (fixes.length > 0) {
    console.log('\n📋 修复详情:');
    console.log('-'.repeat(60));

    fixes.forEach((fix, index) => {
      console.log(`${index + 1}. 任务: ${fix.taskName}`);
      console.log(`   任务ID: ${fix.taskId}`);
      console.log(`   旧依赖: ${fix.oldDependency}`);
      console.log(`   新依赖: ${fix.newDependency}`);
      console.log('');
    });
  }
}

/**
 * 验证修复结果
 */
function validateFixes(taskData) {
  const allTaskIds = new Set(taskData.tasks.map((task) => task.id));
  const brokenDependencies = [];

  taskData.tasks.forEach((task) => {
    if (task.dependencies && Array.isArray(task.dependencies)) {
      task.dependencies.forEach((dep) => {
        const depId = dep.taskId || dep;
        if (!allTaskIds.has(depId)) {
          brokenDependencies.push({
            taskName: task.name,
            taskId: task.id,
            missingDependency: depId,
          });
        }
      });
    }
  });

  return brokenDependencies;
}

// 主执行函数
function main() {
  console.log('🚀 开始修复任务依赖关系...\n');

  // 加载任务数据
  const taskData = loadTaskData();
  if (!taskData) {
    process.exit(1);
  }

  console.log(`📊 当前任务数量: ${taskData.tasks.length}`);

  // 修复依赖关系
  const {
    fixedCount,
    fixes,
    taskData: updatedTaskData,
  } = fixDependencies(taskData);

  // 生成修复报告
  generateFixReport(fixes);

  if (fixedCount > 0) {
    // 保存修复后的数据
    const saved = saveTaskData(updatedTaskData);

    if (saved) {
      // 验证修复结果
      const brokenDeps = validateFixes(updatedTaskData);

      if (brokenDeps.length === 0) {
        console.log('🎉 所有依赖关系修复完成，验证通过！');
        process.exit(0);
      } else {
        console.log('⚠️  仍有未解决的依赖问题:');
        brokenDeps.forEach((dep) => {
          console.log(`   ${dep.taskName}: 缺失依赖 ${dep.missingDependency}`);
        });
        process.exit(1);
      }
    } else {
      process.exit(1);
    }
  } else {
    console.log('ℹ️  没有发现需要修复的依赖关系');
    process.exit(0);
  }
}

if (require.main === module) {
  main();
}

module.exports = {
  ID_MAPPING,
  loadTaskData,
  fixDependencies,
  saveTaskData,
  validateFixes,
};
