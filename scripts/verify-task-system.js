#!/usr/bin/env node

/**
 * 任务系统完整性验证工具
 * 检查任务数据文件中的所有ID格式，验证依赖关系，测试任务管理功能
 */

const fs = require('fs');
const path = require('path');
const crypto = require('crypto');

// UUID v4 正则表达式
const UUID_V4_REGEX =
  /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;

/**
 * 验证ID是否为有效的UUID v4
 */
function isValidUUIDv4(id) {
  return UUID_V4_REGEX.test(id);
}

/**
 * 读取任务数据文件
 */
function loadTaskData() {
  const taskDataPath = path.join(process.cwd(), 'docs/data/tasks.json');

  if (!fs.existsSync(taskDataPath)) {
    console.log('❌ 任务数据文件不存在:', taskDataPath);
    return null;
  }

  try {
    const data = fs.readFileSync(taskDataPath, 'utf8');
    return JSON.parse(data);
  } catch (error) {
    console.log('❌ 读取任务数据文件失败:', error.message);
    return null;
  }
}

/**
 * 验证任务数据完整性
 */
function validateTaskData(taskData) {
  if (!taskData || !taskData.tasks || !Array.isArray(taskData.tasks)) {
    return {
      valid: false,
      error: '任务数据格式无效',
    };
  }

  const results = {
    valid: true,
    totalTasks: taskData.tasks.length,
    validIds: [],
    invalidIds: [],
    dependencyIssues: [],
    missingFields: [],
  };

  // 收集所有任务ID
  const allTaskIds = new Set();

  taskData.tasks.forEach((task, index) => {
    // 检查必需字段
    const requiredFields = ['id', 'name', 'description', 'status'];
    const missing = requiredFields.filter((field) => !task[field]);
    if (missing.length > 0) {
      results.missingFields.push({
        taskIndex: index,
        taskName: task.name || `任务${index}`,
        missingFields: missing,
      });
    }

    // 检查ID格式
    if (task.id) {
      allTaskIds.add(task.id);
      if (isValidUUIDv4(task.id)) {
        results.validIds.push({
          id: task.id,
          name: task.name,
        });
      } else {
        results.invalidIds.push({
          id: task.id,
          name: task.name,
          newId: crypto.randomUUID(),
        });
      }
    }
  });

  // 检查依赖关系
  taskData.tasks.forEach((task, index) => {
    if (task.dependencies && Array.isArray(task.dependencies)) {
      task.dependencies.forEach((dep) => {
        const depId = dep.taskId || dep;
        if (!allTaskIds.has(depId)) {
          results.dependencyIssues.push({
            taskId: task.id,
            taskName: task.name,
            missingDependency: depId,
          });
        }
      });
    }
  });

  if (
    results.invalidIds.length > 0 ||
    results.dependencyIssues.length > 0 ||
    results.missingFields.length > 0
  ) {
    results.valid = false;
  }

  return results;
}

/**
 * 生成详细报告
 */
function generateReport(results) {
  console.log('🔍 任务系统完整性验证报告');
  console.log('='.repeat(60));

  if (results.valid) {
    console.log('✅ 任务系统验证通过');
  } else {
    console.log('❌ 任务系统存在问题');
  }

  console.log(`\n📊 统计信息:`);
  console.log(`   总任务数: ${results.totalTasks}`);
  console.log(`   有效ID: ${results.validIds.length}`);
  console.log(`   无效ID: ${results.invalidIds.length}`);
  console.log(`   依赖问题: ${results.dependencyIssues.length}`);
  console.log(`   字段缺失: ${results.missingFields.length}`);

  if (results.invalidIds.length > 0) {
    console.log('\n❌ 无效的任务ID:');
    console.log('-'.repeat(40));
    results.invalidIds.forEach((item) => {
      console.log(`   ${item.name}`);
      console.log(`   旧ID: ${item.id}`);
      console.log(`   新ID: ${item.newId}`);
      console.log('');
    });
  }

  if (results.dependencyIssues.length > 0) {
    console.log('\n🔗 依赖关系问题:');
    console.log('-'.repeat(40));
    results.dependencyIssues.forEach((issue) => {
      console.log(`   任务: ${issue.taskName} (${issue.taskId})`);
      console.log(`   缺失依赖: ${issue.missingDependency}`);
      console.log('');
    });
  }

  if (results.missingFields.length > 0) {
    console.log('\n📝 字段缺失问题:');
    console.log('-'.repeat(40));
    results.missingFields.forEach((issue) => {
      console.log(`   任务: ${issue.taskName}`);
      console.log(`   缺失字段: ${issue.missingFields.join(', ')}`);
      console.log('');
    });
  }

  return results;
}

/**
 * 测试任务验证功能
 */
function testTaskVerification() {
  console.log('\n🧪 测试任务验证功能...');

  // 测试有效的UUID v4
  const validIds = [
    'b51718cc-**************-1c082964f30b',
    '1ea07a45-4606-4217-bb3f-7cd5d26272cf',
    'c0fa19a7-8bc1-48a6-881f-3989314eb4bc',
  ];

  // 测试无效的ID
  const invalidIds = [
    'a1b2c3d4-e5f6-7890-abcd-ef1234567890',
    'sentry-monitoring-core-2025-0728',
    'gh-actions-001-2025-0728',
  ];

  console.log('✅ 有效ID测试:');
  validIds.forEach((id) => {
    const isValid = isValidUUIDv4(id);
    console.log(`   ${id}: ${isValid ? '✅' : '❌'}`);
  });

  console.log('\n❌ 无效ID测试:');
  invalidIds.forEach((id) => {
    const isValid = isValidUUIDv4(id);
    console.log(
      `   ${id}: ${isValid ? '❌ 应该无效但通过了' : '✅ 正确识别为无效'}`,
    );
  });
}

// 主执行函数
function main() {
  console.log('🚀 开始验证任务系统...\n');

  // 加载任务数据
  const taskData = loadTaskData();
  if (!taskData) {
    process.exit(1);
  }

  // 验证任务数据
  const results = validateTaskData(taskData);

  // 生成报告
  generateReport(results);

  // 测试验证功能
  testTaskVerification();

  // 返回结果
  if (results.valid) {
    console.log('\n🎉 任务系统验证完成，所有检查通过！');
    process.exit(0);
  } else {
    console.log('\n⚠️  任务系统存在问题，需要修复！');
    process.exit(1);
  }
}

if (require.main === module) {
  main();
}

module.exports = {
  isValidUUIDv4,
  loadTaskData,
  validateTaskData,
  generateReport,
};
