#!/usr/bin/env node

/**
 * 任务ID修复工具
 * 基于实际任务列表分析和修复无效的任务ID
 */

const crypto = require('crypto');

// 从任务列表中提取的所有无效ID
const INVALID_TASK_IDS = [
  'a1b2c3d4-e5f6-7890-abcd-ef1234567890', // P0级架构一致性检查配置
  'a1b2c3d4-e5f6-7890-abcd-ef1234567891', // P0级安全扫描强化配置
  'a1b2c3d4-e5f6-7890-abcd-ef1234567892', // P0级性能预算控制配置
  'a1b2c3d4-e5f6-7890-abcd-ef1234567893', // P0级代码重复度检测配置
  'sentry-monitoring-core-2025-0728', // 基础错误监控与可观察性配置
  'gh-actions-001-2025-0728', // GitHub Actions基础CI/CD配置
  'renovate-001-2025-0728', // Renovate依赖管理配置
  'a2b3c4d5-e6f7-8901-bcde-f23456789012', // SEO优化配置
];

// 任务名称映射（用于生成更清晰的修复报告）
const TASK_NAMES = {
  'a1b2c3d4-e5f6-7890-abcd-ef1234567890':
    'P0级架构一致性检查配置（dependency-cruiser + madge）',
  'a1b2c3d4-e5f6-7890-abcd-ef1234567891':
    'P0级安全扫描强化配置（eslint-plugin-security-node + semgrep）',
  'a1b2c3d4-e5f6-7890-abcd-ef1234567892':
    'P0级性能预算控制配置（size-limit + bundle分析）',
  'a1b2c3d4-e5f6-7890-abcd-ef1234567893':
    'P0级代码重复度检测配置（jscpd + 重复度分析）',
  'sentry-monitoring-core-2025-0728':
    '基础错误监控与可观察性配置（Sentry核心监控）',
  'gh-actions-001-2025-0728': 'GitHub Actions基础CI/CD配置',
  'renovate-001-2025-0728': 'Renovate依赖管理配置',
  'a2b3c4d5-e6f7-8901-bcde-f23456789012':
    'SEO优化配置（next-sitemap + 结构化数据 + metadata工具函数）',
};

/**
 * 生成新的UUID v4映射
 */
function generateNewIds() {
  const mapping = new Map();

  INVALID_TASK_IDS.forEach((oldId) => {
    mapping.set(oldId, crypto.randomUUID());
  });

  return mapping;
}

/**
 * 生成详细的修复报告
 */
function generateDetailedReport() {
  const idMapping = generateNewIds();

  console.log('🔧 任务ID修复报告');
  console.log('='.repeat(60));
  console.log(`发现 ${INVALID_TASK_IDS.length} 个无效的任务ID需要修复\n`);

  console.log('📋 ID映射表:');
  console.log('-'.repeat(60));

  idMapping.forEach((newId, oldId) => {
    const taskName = TASK_NAMES[oldId] || '未知任务';
    console.log(`任务: ${taskName}`);
    console.log(`旧ID: ${oldId}`);
    console.log(`新ID: ${newId}`);
    console.log('');
  });

  console.log('🚀 修复步骤:');
  console.log('-'.repeat(60));
  console.log('1. 使用任务管理工具更新每个任务的ID');
  console.log('2. 更新所有依赖关系中的引用');
  console.log('3. 验证修复后的ID格式正确性');
  console.log('4. 测试任务管理功能是否正常');

  console.log('\n⚠️  注意事项:');
  console.log('-'.repeat(60));
  console.log('- 更新ID时需要同时更新依赖关系');
  console.log('- 建议按依赖顺序逐个更新');
  console.log('- 更新后需要验证所有功能正常');

  return idMapping;
}

/**
 * 生成JSON格式的映射文件
 */
function generateMappingFile(mapping) {
  const mappingObject = {};
  mapping.forEach((newId, oldId) => {
    mappingObject[oldId] = {
      newId: newId,
      taskName: TASK_NAMES[oldId] || '未知任务',
    };
  });

  return JSON.stringify(mappingObject, null, 2);
}

// 主执行函数
function main() {
  console.log('🔍 开始分析任务ID问题...\n');

  const mapping = generateDetailedReport();

  console.log('\n💾 生成映射文件...');
  const mappingJson = generateMappingFile(mapping);

  // 可以选择将映射保存到文件
  const fs = require('fs');
  fs.writeFileSync('task-id-mapping.json', mappingJson);
  console.log('✅ 映射文件已保存到: task-id-mapping.json');

  console.log('\n🎯 下一步操作:');
  console.log('1. 检查 task-id-mapping.json 文件');
  console.log('2. 使用任务管理工具逐个更新ID');
  console.log('3. 验证修复结果');
}

if (require.main === module) {
  main();
}

module.exports = {
  INVALID_TASK_IDS,
  TASK_NAMES,
  generateNewIds,
  generateDetailedReport,
  generateMappingFile,
};
