#!/usr/bin/env node

/**
 * 企业级翻译同步工具
 * 确保所有语言的翻译文件结构完全一致
 */

const fs = require('fs');
const path = require('path');

console.log('🔄 开始翻译同步检查...\n');

const LOCALES = ['en', 'zh'];
const MESSAGES_DIR = path.join(process.cwd(), 'messages');

let syncIssues = [];
let syncWarnings = [];
let totalKeys = 0;
let syncedKeys = 0;

/**
 * 加载翻译文件
 */
function loadTranslations() {
  const translations = {};

  for (const locale of LOCALES) {
    const filePath = path.join(MESSAGES_DIR, `${locale}.json`);
    try {
      const content = fs.readFileSync(filePath, 'utf8');
      translations[locale] = JSON.parse(content);
      console.log(`✅ 加载 ${locale}.json`);
    } catch (error) {
      console.error(`❌ 无法加载 ${locale}.json: ${error.message}`);
      process.exit(1);
    }
  }

  return translations;
}

/**
 * 提取所有键路径
 */
function extractAllKeys(obj, prefix = '') {
  const keys = [];

  for (const [key, value] of Object.entries(obj)) {
    const fullKey = prefix ? `${prefix}.${key}` : key;

    if (value && typeof value === 'object' && !Array.isArray(value)) {
      keys.push(...extractAllKeys(value, fullKey));
    } else {
      keys.push(fullKey);
    }
  }

  return keys;
}

/**
 * 获取嵌套值
 */
function getNestedValue(obj, path) {
  return path.split('.').reduce((current, key) => current?.[key], obj);
}

/**
 * 设置嵌套值
 */
function setNestedValue(obj, path, value) {
  const keys = path.split('.');
  const lastKey = keys.pop();
  const target = keys.reduce((current, key) => {
    if (!current[key] || typeof current[key] !== 'object') {
      current[key] = {};
    }
    return current[key];
  }, obj);
  target[lastKey] = value;
}

/**
 * 检查翻译同步状态
 */
function checkTranslationSync(translations) {
  console.log('🔍 检查翻译同步状态...\n');

  // 收集所有键
  const allKeysSet = new Set();
  for (const locale of LOCALES) {
    const keys = extractAllKeys(translations[locale]);
    keys.forEach((key) => allKeysSet.add(key));
  }

  const allKeys = Array.from(allKeysSet).sort();
  totalKeys = allKeys.length;

  console.log(`📊 发现 ${totalKeys} 个翻译键\n`);

  // 检查每个键在所有语言中的存在性
  const missingKeys = {};
  const inconsistentTypes = {};
  const emptyValues = {};
  const suspiciousTranslations = {};

  for (const key of allKeys) {
    const values = {};
    const types = {};

    for (const locale of LOCALES) {
      const value = getNestedValue(translations[locale], key);
      values[locale] = value;
      types[locale] = typeof value;

      // 检查缺失键
      if (value === undefined) {
        if (!missingKeys[locale]) missingKeys[locale] = [];
        missingKeys[locale].push(key);
      }

      // 检查空值
      if (value === '' || (typeof value === 'string' && value.trim() === '')) {
        if (!emptyValues[locale]) emptyValues[locale] = [];
        emptyValues[locale].push(key);
      }
    }

    // 检查类型一致性
    const uniqueTypes = new Set(
      Object.values(types).filter((t) => t !== 'undefined'),
    );
    if (uniqueTypes.size > 1) {
      inconsistentTypes[key] = types;
    }

    // 检查可疑的未翻译内容
    const stringValues = Object.entries(values)
      .filter(([_, value]) => typeof value === 'string' && value.trim() !== '')
      .map(([locale, value]) => ({ locale, value }));

    if (stringValues.length > 1) {
      const firstValue = stringValues[0].value;
      const sameValues = stringValues.filter(
        ({ value }) => value === firstValue,
      );

      // 如果所有值都相同且不是特殊键，标记为可疑
      if (
        sameValues.length === stringValues.length &&
        !key.includes('title') &&
        !key.includes('url') &&
        firstValue.length > 3
      ) {
        suspiciousTranslations[key] = firstValue;
      }
    }

    // 如果键在所有语言中都存在，计为已同步
    if (Object.values(values).every((v) => v !== undefined)) {
      syncedKeys++;
    }
  }

  // 报告结果
  console.log('📋 同步检查结果:\n');

  // 缺失键
  for (const [locale, keys] of Object.entries(missingKeys)) {
    if (keys.length > 0) {
      console.log(`❌ ${locale} 缺失 ${keys.length} 个键:`);
      keys.slice(0, 5).forEach((key) => console.log(`   - ${key}`));
      if (keys.length > 5) {
        console.log(`   ... 还有 ${keys.length - 5} 个`);
      }
      console.log();

      syncIssues.push({
        type: 'missing_keys',
        locale,
        count: keys.length,
        keys: keys,
      });
    }
  }

  // 空值
  for (const [locale, keys] of Object.entries(emptyValues)) {
    if (keys.length > 0) {
      console.log(`⚠️  ${locale} 有 ${keys.length} 个空值:`);
      keys.slice(0, 3).forEach((key) => console.log(`   - ${key}`));
      if (keys.length > 3) {
        console.log(`   ... 还有 ${keys.length - 3} 个`);
      }
      console.log();

      syncWarnings.push({
        type: 'empty_values',
        locale,
        count: keys.length,
        keys: keys,
      });
    }
  }

  // 类型不一致
  if (Object.keys(inconsistentTypes).length > 0) {
    console.log(
      `⚠️  发现 ${Object.keys(inconsistentTypes).length} 个类型不一致的键:`,
    );
    Object.entries(inconsistentTypes)
      .slice(0, 3)
      .forEach(([key, types]) => {
        console.log(`   - ${key}: ${JSON.stringify(types)}`);
      });
    console.log();

    syncWarnings.push({
      type: 'type_inconsistency',
      count: Object.keys(inconsistentTypes).length,
      keys: Object.keys(inconsistentTypes),
    });
  }

  // 可疑翻译
  if (Object.keys(suspiciousTranslations).length > 0) {
    console.log(
      `🤔 发现 ${Object.keys(suspiciousTranslations).length} 个可疑的未翻译内容:`,
    );
    Object.entries(suspiciousTranslations)
      .slice(0, 3)
      .forEach(([key, value]) => {
        console.log(`   - ${key}: "${value}"`);
      });
    console.log();

    syncWarnings.push({
      type: 'suspicious_translations',
      count: Object.keys(suspiciousTranslations).length,
      keys: Object.keys(suspiciousTranslations),
    });
  }

  return {
    allKeys,
    missingKeys,
    inconsistentTypes,
    emptyValues,
    suspiciousTranslations,
  };
}

/**
 * 生成同步报告
 */
function generateSyncReport(syncResult) {
  const syncRate =
    totalKeys > 0 ? ((syncedKeys / totalKeys) * 100).toFixed(1) : 100;

  console.log('📊 同步统计:\n');
  console.log(`   总键数: ${totalKeys}`);
  console.log(`   已同步: ${syncedKeys}`);
  console.log(`   同步率: ${syncRate}%`);
  console.log(`   问题数: ${syncIssues.length}`);
  console.log(`   警告数: ${syncWarnings.length}\n`);

  // 生成详细报告文件
  const report = {
    timestamp: new Date().toISOString(),
    summary: {
      totalKeys,
      syncedKeys,
      syncRate: parseFloat(syncRate),
      issueCount: syncIssues.length,
      warningCount: syncWarnings.length,
    },
    issues: syncIssues,
    warnings: syncWarnings,
    details: syncResult,
  };

  const reportPath = path.join(
    process.cwd(),
    'reports',
    'translation-sync-report.json',
  );

  // 确保报告目录存在
  const reportsDir = path.dirname(reportPath);
  if (!fs.existsSync(reportsDir)) {
    fs.mkdirSync(reportsDir, { recursive: true });
  }

  fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
  console.log(`📄 详细报告已保存到: ${reportPath}\n`);

  return parseFloat(syncRate);
}

/**
 * 主函数
 */
function main() {
  try {
    const translations = loadTranslations();
    const syncResult = checkTranslationSync(translations);
    const syncRate = generateSyncReport(syncResult);

    // 判断是否通过
    const hasIssues = syncIssues.length > 0;
    const hasWarnings = syncWarnings.length > 0;

    if (!hasIssues && !hasWarnings) {
      console.log('✅ 翻译同步检查通过！所有翻译文件完全同步。\n');
      process.exit(0);
    } else if (!hasIssues && hasWarnings) {
      console.log('⚠️  翻译同步检查通过，但有警告需要注意。\n');
      process.exit(0);
    } else {
      console.log('❌ 翻译同步检查失败！存在需要修复的问题。\n');
      process.exit(1);
    }
  } catch (error) {
    console.error('💥 翻译同步检查失败:', error.message);
    process.exit(1);
  }
}

// 运行主函数
main();
