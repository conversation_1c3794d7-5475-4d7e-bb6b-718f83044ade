name: 轻量级质量检查

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

jobs:
  lightweight-quality:
    runs-on: ubuntu-latest
    
    steps:
    - name: 检出代码
      uses: actions/checkout@v4
      with:
        fetch-depth: 2  # 需要获取前一个提交用于diff

    - name: 设置Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '20'
        cache: 'npm'

    - name: 设置pnpm
      uses: pnpm/action-setup@v4
      with:
        version: 8

    - name: 安装依赖
      run: pnpm install --frozen-lockfile

    - name: 运行轻量级质量检查
      run: pnpm quality:quick:verbose

    - name: 生成质量报告
      run: pnpm quality:report
      continue-on-error: true

    - name: 上传质量报告
      uses: actions/upload-artifact@v4
      if: always()
      with:
        name: quality-reports
        path: |
          reports/quick-quality-report.json
          reports/simple-quality-report.json
          reports/simple-quality-report.html
        retention-days: 30

    - name: 评论PR（如果是PR）
      if: github.event_name == 'pull_request'
      uses: actions/github-script@v7
      with:
        script: |
          const fs = require('fs');
          const path = './reports/simple-quality-report.json';
          
          if (fs.existsSync(path)) {
            const report = JSON.parse(fs.readFileSync(path, 'utf8'));
            const score = report.summary.overallScore;
            const passed = report.summary.passedChecks;
            const total = report.summary.totalChecks;
            
            const comment = `## 🔍 轻量级质量检查报告
            
**总体分数**: ${score}/100
**检查结果**: ${passed}/${total} 通过

### 📊 项目统计
- **源文件**: ${report.stats.totalFiles} 个
- **代码行数**: ${report.stats.codeLines} 行
- **测试覆盖**: ${report.stats.testCoverage}%

### ✅ 检查详情
${report.checks.map(check => 
  `- ${check.status === 'PASS' ? '✅' : '❌'} ${check.name}`
).join('\n')}

${report.recommendations.length > 0 ? `
### 💡 改进建议
${report.recommendations.map((rec, i) => 
  `${i+1}. **[${rec.priority}]** ${rec.title}: ${rec.description}`
).join('\n')}
` : ''}

---
*此报告由轻量级质量检查系统自动生成*`;

            github.rest.issues.createComment({
              issue_number: context.issue.number,
              owner: context.repo.owner,
              repo: context.repo.repo,
              body: comment
            });
          }
